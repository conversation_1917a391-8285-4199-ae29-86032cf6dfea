<template>
    <!-- 工单服务 -->
    <div class="order common">
        <!-- 背景与标题区域 -->
        <div class="order-bg common-bg text-left">
            <div class="order-title common-title padding-left-xll">工单服务</div>
            <img src="../assets/img/order-service.png" alt="工单服务图标">
        </div>

        <!-- 工单信息  -->
        <div class="order-sub-bg">
            <div class="order-sub-title font-size-xs">年度</div>
            <div class="clear-both"></div>
            <el-row v-if="!loading" class="row-bg  padding-tb-sm" justify="space-between">
                <el-col :span="6">
                    <div class="grid-content ep-bg-purple align-center">
                        <div class="flex justify-center align-center">
                            <div class="sub-bg">
                                <canvas ref="completionCanvas"></canvas>
                                <el-statistic class="Roboto" :value="completionRateOutput">
                                </el-statistic>
                            </div>
                            <span class="font-size-sm color-54 ren">%</span>
                        </div>
                        <div class="color-blue font-size-df">办结率</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content ep-bg-purple align-center">
                        <div class="flex justify-center align-center">
                            <div class="sub-bg">
                                <canvas ref="validCallCanvas"></canvas>
                                <el-statistic class="Roboto" :value="validCallRateOutput"> </el-statistic>
                            </div>
                            <span class="font-size-sm color-54 ren">%</span>
                        </div>
                        <div class="color-blue font-size-df">有效回访率</div>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content ep-bg-purple align-center">
                        <div class="flex justify-center align-center">
                            <div class="sub-bg">
                                <canvas ref="satisfactionCanvas"></canvas>
                                <el-statistic class="Roboto" :value="satisfactionRateOutput"> </el-statistic>
                            </div>
                            <span class="font-size-sm color-54 ren">%</span>
                        </div>
                        <div class="color-blue font-size-df">满意度</div>
                    </div>
                </el-col>
            </el-row>
            <div v-else class="loading-container">
                <el-spinner class="loading-spinner" size="large"></el-spinner>
                <div class="loading-text">数据加载中...</div>
            </div>
        </div>

        <!-- 受诉量区域 -->
        <div class="common-subtitle order-subtitle">
            <div class="flex space-between align-end margin-tb-xxs">
                <div class="flex nowarp">
                    <img src="../assets/img/icon.png" alt="图标">
                    <div class="margin-left-xs">受诉量</div>
                </div>
                <div class="complaint-tab flex align-center font-size-sm">
                    <div v-for="(tab, index) in tabs" :key="tab.id"
                        :class="{ 'active color-E3AE3B': activeTab === index, 'default color-6c': activeTab !== index }"
                        @click="setActiveTab(index)">
                        {{ tab.label }}
                    </div>
                </div>
            </div>
            <div class="liness"></div>

            <!-- 受诉量数据内容 - 添加加载状态 -->
            <div v-if="!loading1" class="order-complaint-bg margin-top-xs">
                <div class="common-layout">
                    <el-row class="row-bg " style="margin: 0;" :gutter="20">
                        <el-col v-if="activeTab === 0" :span="8"
                            v-for="(item, index) in complaintData.filter((_, i) => [0, 2, 3, 4, 5, 6].includes(i))    "
                            :key="item.type" class="flex space-between align-center data-item">
                            <div class="c-img" v-if="index === 0 || index === 3">
                                <img class="c-img" v-if="index === 0" src="../assets/img/c-b.png" alt="受诉量图标">
                                <img class="c-img" v-if="index === 3" src="../assets/img/c-consulting.png" alt="咨询量图标">
                            </div>
                            <div class="text-left c-b data-content">
                                <div class="flex align-center color-blue title-container">
                                    <div class="color-blue font-size-df flex align-center title-wrapper">
                                        <div v-if="index !== 0 && index !== 3" class="c-tag"></div>
                                        <div class="title-text">{{ item.title }}</div>
                                    </div>
                                </div>
                                <div class="Roboto seats-title count-container flex align-center">
                                    <div v-if="index !== 0 && index !== 3" class="common-kongge"></div>
                                    <el-statistic :value-style="{
                                        fontSize: '24px', fontFamily: 'DIN-Bold', fontWeight: 'bolder',
                                        color: 'transparent',
                                        background: 'linear-gradient(0deg, #86cbf0 0%, #ffffff 90%)',
                                        '-webkit-background-clip': 'text',
                                    }" :value="getCountOutput(item.count, item.type).value">
                                    </el-statistic>
                                    <span v-if="index == 2 || index == 5"
                                        class="font-size-sm color-54 ren margin-left-xs">%</span>
                                    <span v-else class="font-size-sm color-54 ren margin-left-xs">件</span>
                                </div>
                            </div>
                        </el-col>
                        <el-col v-if="activeTab !== 0" :span="getSpan(index)" @click="showTModal(item.title, index)"
                            v-for="( item, index ) in  complaintData.filter((_, i) => [0, 1, 2, 4].includes(i))    "
                            :key="item.type" class="flex space-between align-center pointer data-item">
                            <div class="c-img" v-if="index === 0 || index === 3">
                                <img class="c-img" v-if="index === 0" src="../assets/img/c-b.png" alt="受诉量图标">
                                <img class="c-img" v-if="index === 3" src="../assets/img/c-consulting.png" alt="咨询量图标">
                            </div>
                            <div class="text-left c-b c-bs data-content">
                                <div class="flex align-center space-between">
                                    <div>
                                        <div class="flex align-center color-blue title-container">
                                            <div class="color-blue font-size-df flex align-center title-wrapper">
                                                <div v-if="index !== 0 && index !== 3" class="c-tag"></div>
                                                <div class="title-text">{{ item.title }}</div>
                                            </div>
                                            <div class="margin-left-sm font-size-sm">查看></div>
                                        </div>
                                        <div class="Roboto  seats-title count-container flex align-center">
                                            <div v-if="index !== 0 && index !== 3" class="common-kongge"></div>
                                            <el-statistic :value-style="{
                                                fontSize: '24px', fontFamily: 'DIN-Bold', fontWeight: 'bolder',
                                                color: 'transparent',
                                                background: 'linear-gradient(0deg, #86cbf0 0%, #ffffff 90%)',
                                                '-webkit-background-clip': 'text',
                                            }" :value="getCountOutput(item.count, item.type).value">
                                            </el-statistic>
                                            <span class="font-size-sm color-54 ren margin-left-xs">件</span>
                                        </div>
                                    </div>
                                    <div class="flex align-center" style="  white-space: nowrap;"
                                        v-if="index === 0 || index === 3">
                                        <div class="line"></div>
                                        <div class="flex align-center num-right  padding-left">
                                            <div class="color-blue">环比：</div>
                                            <el-statistic precision="2" style="padding: 0;"
                                                :class="item.subValueClass + ' seats-title'"
                                                :value="getSubValueOutput(item.subValue, item.type).value">

                                            </el-statistic>
                                            <span class="font-size-sm color-54 ren margin-left-xs">%</span>


                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </div>
            <div v-else class="loading-container">
                <el-spinner class="loading-spinner" size="large"></el-spinner>
                <div class="loading-text">数据加载中...</div>
            </div>
        </div>

        <!-- 渠道分析区域 -->
        <div class="common-subtitle">
            <div class="flex nowarp">
                <img src="../assets/img/icon.png" alt="图标">
                <div class="margin-left-xs">渠道分析</div>
            </div>
            <div class="liness"></div>

            <!-- 内容  -->
            <div v-if="!loading2" class="margin-top-xs">
                <el-row class="row-bg align-center" style="height: 180px;" :gutter="20">
                    <el-col :span="16" class="flex align-center" style="position: relative;">
                        <!-- ECharts 容器 -->
                        <ECharts3DPie :chartData="chartData" @mouseenter="pauseCarousel"
                            @mouseleave="resumeCarouselWithDelay" :colors="colors" @pie-click="handlePieClick" />
                        <div class="common-zhexian pointer" @click="handleViewClick(currentChannel)">
                            <div class="color-blue flex space-between" style="padding-left:10px;">{{ currentChannel.name }}
                                <span>查看></span>
                            </div>
                            <div class="advantage_line2"></div>
                            <div class="chartvalue">{{ formatNumber(currentChannel.value) }}件</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="margin-right-lg font-size-sm">
                            <div v-for="(item, index) in chartData" @mouseenter="pauseCarousel"
                                @mouseleave="resumeCarouselWithDelay" @click="handleItemClick(item, index)" :key="index"
                                class="flex align-center space-between pointer"
                                :class="[index > 0 ? 'margin-top-xs' : '', index === currentIndex ? 'active-channel' : '']">
                                <div class="flex align-center">
                                    <div class="common-dian" :class="`bg-${getBgColorClass(index)}`"></div>
                                    <div class="color-blue margin-left-xs">{{ item.name }}</div>
                                </div>
                                <div>
                                    <el-statistic class="color-white" :value="getValueOutput(item.value, item.type).value">
                                        <template #suffix>
                                            <span class="color-54 margin-left-xs">件</span>
                                        </template>
                                    </el-statistic>
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div v-else class="loading-container">
                <el-spinner class="loading-spinner" size="large"></el-spinner>
                <div class="loading-text">数据加载中...</div>
            </div>
        </div>


        <!-- 表单 -->
        <div v-if="isModalT" class="dialog">
            <div class="dialog-bg" style="height: 812px;max-height: 812px;">
                <div class="d-t  flex space-between align-center">
                    <img class="d-t-b" src="../assets/img/order-d-s.png" alt="">
                    <div class="dialog-title common-title">{{ activeTab == 1 ? '当天' : activeTab == 2 ? '本周' : activeTab ==
                        1 ? '本月' : '本年' }} -工单服务{{ dtitle }}</div>
                    <div class="d-t-c pointer" @click="closeD" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave"
                        @mousedown="handleMouseDown" @mouseup="handleMouseUp">
                        <img :src="currentImage" class="">
                        <!-- 预加载所有状态图片 -->
                        <img v-if="false" :src="normalImage" alt="预加载">
                        <img v-if="false" :src="hoverImage" alt="预加载">
                        <img v-if="false" :src="clickImage" alt="预加载">
                    </div>
                </div>
                <!-- 内容 -->
                <div class="dialog-content">
                    <div class="complaint-table-container">
                        <el-table :data="tableData" stripe highlight-current-row class="margin-top" style="height: 635px;">
                            <el-table-column type="index" label="序号" width="60"></el-table-column>
                            <el-table-column prop="flags" label="标识" width="150">
                                <template #default="scope">
                                    <div class="flex justify-center">
                                        <el-text class="margin-right-xs" v-if="scope.row.extendFlag"
                                            type="primary1">延</el-text>
                                        <el-text class="margin-right-xs" v-if="scope.row.timeoutFlag"
                                            type="danger">超</el-text>
                                        <el-text class="margin-right-xs" v-if="scope.row.remindFlag"
                                            type="warning">催</el-text>
                                        <el-text class="margin-right-xs" v-if="scope.row.redispatchFlag"
                                            type="primary">重</el-text>
                                        <el-text class="margin-right-xs" v-if="scope.row.repeatedEvent"
                                            type="primary2">复</el-text>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="dispatchStatusName" label="状态" width="100">
                                <template #default="scope">
                                    <el-text :type="getStatusType(scope.row.dispatchStatusName)">{{
                                        scope.row.dispatchStatusName }}</el-text>
                                </template>
                            </el-table-column>
                            <el-table-column prop="billNo" label="客诉单号" width="200"></el-table-column>
                            <el-table-column prop="occurAddress" label="发生地址" width="150"></el-table-column>
                            <el-table-column prop="issueSectionName" label="问题板块" width="120"></el-table-column>
                            <el-table-column prop="reportTypeName" label="反映类型" width="120"></el-table-column>
                            <el-table-column prop="reflectionSourceName" label="反映来源" width="120"></el-table-column>
                            <el-table-column prop="issueReasonName" label="问题原因" width="200"></el-table-column>
                            <el-table-column prop="issueDescription" label="问题描述" width="200">
                                <template #default="scope">
                                    <el-tooltip v-if="scope.row.issueDescription" :content="scope.row.issueDescription"
                                        :disabled="scope.row.issueDescription.length <= 20" placement="top"
                                        effect="customized" :open-delay="300">
                                        <!-- 显示的内容（截断+2行） -->
                                        <div class="issue-description" v-if="scope.row.issueDescription.length >= 20">
                                            {{ formatIssueDesc(scope.row.issueDescription) }}
                                        </div>
                                        <div v-else>{{ scope.row.issueDescription }}</div>

                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column prop="reportTime" label="接报时间" width="200"></el-table-column>
                        </el-table>
                        <!-- 分页控制 -->
                        <el-pagination class="margin-top-xs" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" :current-page="currentPage" :page-size="pageSize"
                            layout="total,->,  pager,sizes, jumper,>-" :total="total">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>
        <!-- 渠道分析弹窗 -->
        <div v-if="isModalChannel" class="dialog">
            <div class="dialog-bg" style="height: 812px;max-height: 812px;">
                <div class="d-t  flex space-between align-center">
                    <img class="d-t-b" src="../assets/img/order-d-s.png" alt="">
                    <div class="dialog-title common-title" style="flex:0.7;">本年-渠道来源{{ currentViewChannel.name }}</div>
                    <div class="d-t-c pointer" @click="closeD" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave"
                        @mousedown="handleMouseDown" @mouseup="handleMouseUp">
                        <img :src="currentImage" class="">
                        <!-- 预加载所有状态图片 -->
                        <img v-if="false" :src="normalImage" alt="预加载">
                        <img v-if="false" :src="hoverImage" alt="预加载">
                        <img v-if="false" :src="clickImage" alt="预加载">
                    </div>
                </div>
                <!-- 内容 -->
                <div class="dialog-content">
                    <div class="complaint-table-container">
                        <el-table class="margin-top" :data="tableChannelData" style="width:100%;" stripe
                            highlight-current-row height="635px">
                            <el-table-column type="index" label="序号" width="60"></el-table-column>
                            <el-table-column prop="dispatchStatusName" label="状态" width="150">
                                <template #default="scope">
                                    <el-text :type="getStatusType(scope.row.dispatchStatusName)">{{
                                        scope.row.dispatchStatusName }}</el-text>
                                </template>
                            </el-table-column>
                            <el-table-column prop="billNo" label="客诉单号" width="200"></el-table-column>
                            <el-table-column prop="occurAddress" label="发生地址" width="150"></el-table-column>
                            <el-table-column prop="issueSectionName" label="问题板块" width="120"></el-table-column>
                            <el-table-column prop="reportTypeName" label="反映类型" width="120"></el-table-column>
                            <el-table-column prop="reflectionSourceName" label="反映来源" width="120"></el-table-column>
                            <el-table-column prop="issueReasonName" label="问题原因" width="200">
                                <template #default="scope">
                                    <el-tooltip v-if="scope.row.issueReasonName" :content="scope.row.issueReasonName"
                                        :disabled="scope.row.issueReasonName.length <= 20" placement="top"
                                        effect="customized" :open-delay="300">
                                        <!-- 显示的内容（截断+2行） -->
                                        <div class="issue-description" v-if="scope.row.issueReasonName.length >= 20">
                                            {{ formatIssueDesc(scope.row.issueReasonName) }}
                                        </div>
                                        <div v-else>{{ scope.row.issueReasonName }}</div>

                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column prop="processingUnitName" label="处理单位" width="150"></el-table-column>
                            <el-table-column prop="callNumber" label="来电号码" width="130"></el-table-column>
                            <el-table-column prop="contactNumber" label="联系电话" width="130"></el-table-column>
                            <el-table-column prop="acceptanceOpinion" label="回访结果" width="200">
                                <template #default="scope">
                                    <el-tooltip :content="scope.row.acceptanceOpinion" v-if="scope.row.acceptanceOpinion"
                                        :disabled="scope.row.acceptanceOpinion.length <= 20" placement="top"
                                        effect="customized" :open-delay="300">
                                        <div class="issue-description" v-if="scope.row.acceptanceOpinion.length >= 20">
                                            {{ formatIssueDesc(scope.row.acceptanceOpinion) }}
                                        </div>
                                        <div v-else>{{ scope.row.acceptanceOpinion }}</div>

                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column prop="reflectionSourceName" label="回访方式" width="200"></el-table-column>
                        </el-table>
                        <!-- 分页控制 -->
                        <el-pagination class="margin-top-xs" @size-change="handleCSizeChange"
                            @current-change="handleCCurrentChange" :current-page="currentCPage" :page-size="pageCSize"
                            layout="total,->,  pager,sizes, jumper,>-" :total="totalC">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent, reactive, watch, nextTick, ref, computed, onMounted, onUnmounted, onBeforeMount } from 'vue';
import ECharts3DPie from './ECharts3DPie.vue';
import { get, post } from '../utils/request';

import { ElMessage } from 'element-plus';
import { useTransition } from '@vueuse/core'
import normalImg from '../assets/img/close-btn.png';
import hoverImg from '../assets/img/close-hover.png';
import clickImg from '../assets/img/close-click.png';
// 图片路径
import bgImagePath from '../assets/img/sub-bg.png';
import progressImagePath from '../assets/img/sub-click-bg.png';

export default defineComponent({
    name: 'OrderService',
    components: {
        ECharts3DPie
    },
    setup() {
        // 获取Canvas引用
        const completionCanvas = ref(null);
        const validCallCanvas = ref(null);
        const satisfactionCanvas = ref(null);

        // 绘制进度图的通用函数
        const drawProgress = (canvasRef, progress, mode) => {
            const canvas = canvasRef.value;
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            if (!ctx) return;

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 创建图像对象
            const bgImg = new Image();
            const progressImg = new Image();

            // 绘制背景图
            bgImg.onload = () => {
                ctx.drawImage(bgImg, 0, 0, canvas.width, canvas.height);

                // 绘制进度图
                progressImg.onload = () => {
                    // 根据进度和模式裁剪绘制
                    const ratio = progress.value / 100;

                    ctx.save();

                    if (mode === 'horizontal') {
                        // 水平进度：从左到右
                        ctx.rect(0, 0, canvas.width * ratio, canvas.height);
                    } else if (mode === 'vertical') {
                        // 垂直进度：从下到上
                        ctx.rect(0, canvas.height * (1 - ratio), canvas.width, canvas.height * ratio);
                    } else if (mode === 'circular') {
                        // 圆形进度：顺时针
                        const centerX = canvas.width / 2;
                        const centerY = canvas.height / 2;
                        const radius = Math.min(centerX, centerY);

                        ctx.beginPath();
                        ctx.moveTo(centerX, centerY);

                        // 计算起始角度和结束角度（-90度是12点钟方向）
                        const startAngle = -Math.PI / 2;
                        const endAngle = startAngle + 2 * Math.PI * ratio;

                        ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                        ctx.closePath();
                    }

                    ctx.clip();
                    ctx.drawImage(progressImg, 0, 0, canvas.width, canvas.height);
                    ctx.restore();

                    // 添加进度文本
                    ctx.fillStyle = '#fff';
                    ctx.font = 'bold 14px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(`${progress.value}%`, canvas.width / 2, canvas.height / 2);
                };

                progressImg.src = progressImagePath;
            };

            bgImg.src = bgImagePath;
        };

        // 响应式数据
        const isModalT = ref(false);
        const isModalChannel = ref(false);
        const dtitle = ref('');
        const tabs = ref([
            { id: 1, label: '历年', english: 'calendarYear' },
            { id: 2, label: '日', english: 'day' },
            { id: 3, label: '周', english: 'week' },
            { id: 4, label: '月', english: 'month' },
            { id: 5, label: '年', english: 'year' }
        ]);
        const activeTab = ref(1);
        const complaintData = ref([
            {
                type: 'complaint-count', // 受诉量
                title: '受诉量',
                count: '',
                subValue: '',
                subValueClass: '',
            },
            {
                type: 'processing', // 办理中
                title: '办理中',
                count: '',
            },
            {
                type: 'completed', // 办结件
                title: '办结件',
                count: '',
            },
            {
                type: 'completion-rate', // 办结率
                title: '办结率',
                count: '',
            },
            {
                type: 'consultation', // 咨询量
                title: '咨询量',
                count: '',
                subValue: '',
                subValueClass: ''
            },
            {
                type: 'satisfied', // 满意件
                title: '满意件',
                count: '',
            },
            {
                type: 'satisfaction-rate', // 满意率
                title: '满意率',
                count: '',
            }
        ]);
        const normalImage = ref(normalImg);
        const hoverImage = ref(hoverImg);
        const clickImage = ref(clickImg);
        const currentImage = ref(normalImg);
        const tableData = ref([]);
        const tableChannelData = ref([]);
        const currentPage = ref(1);
        const pageSize = ref(10);
        const total = ref(0);
        const currentCPage = ref(1);
        const pageCSize = ref(10);
        const totalC = ref(0);

        // 原始数据
        const completionRateSource = ref(0);
        const validCallRateSource = ref(0);
        const satisfactionRateSource = ref(0);



        // 存储动态创建的过渡对象
        // const countTransitions = new Map();
        const countStates = reactive({});

        // const subValueTransitions = new Map();
        const subValueStates = reactive({});
        // const valueTransitions = new Map();
        const valueStates = reactive({})
        // 格式化数字显示（添加千位分隔符）
        const formatNumber = (num) => {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        };
        // 获取计数值的过渡对象
        const getCountOutput = (count, type) => {
            if (!type || count === undefined || count === null || isNaN(Number(count))) {
                return { value: 0 };
            }

            const targetValue = Math.floor(Number(count));
            const key = `counter_${type}`;

            // 1. 首次加载：初始化时 current 设为 0，target 设为目标值（触发从 0 开始的滚动）
            if (!countStates[key]) {
                countStates[key] = {
                    current: 0, // 首次从 0 开始
                    target: targetValue,
                    timer: null
                };
                // 强制触发首次动画（从 0 → targetValue）
                const state = countStates[key];
                startAnimation(state, key);
                return { value: 0 }; // 初始显示 0
            }

            const state = countStates[key];

            // 2. 后续更新：目标值变化时才触发动画
            if (state.target !== targetValue) {
                state.target = targetValue;
                startAnimation(state, key); // 从当前值滚动到新目标值
            }

            // 返回当前整数（向下取整）
            return { value: Math.floor(state.current) };
        };

        // 抽离动画逻辑为单独函数（复用）
        const startAnimation = (state, key) => {
            // 清除之前的计时器（避免冲突）
            if (state.timer) {
                clearInterval(state.timer);
            }

            // 启动动画（每16ms更新一次）
            state.timer = setInterval(() => {
                const diff = state.target - state.current;

                // 差值足够小时，直接到位并停止
                if (Math.abs(diff) < 1) {
                    state.current = state.target;
                    clearInterval(state.timer);
                    state.timer = null;
                } else {
                    // 每次移动差值的 10%（平滑减速）
                    state.current += diff * 0.1;
                }

                // 强制更新响应式（关键：确保Vue检测到变化）
                countStates[key] = { ...state };
            }, 16);
        };


        const getValueOutput = (value, type) => {
            if (!type || value === undefined || value === null || isNaN(Number(value))) {
                return { value: 0 };
            }

            const targetValue = Math.floor(Number(value));
            const key = `valueer_${type}`;

            // 1. 首次加载：初始化时 current 设为 0，target 设为目标值（触发从 0 开始的滚动）
            if (!valueStates[key]) {
                valueStates[key] = {
                    current: 0, // 首次从 0 开始
                    target: targetValue,
                    timer: null
                };
                // 强制触发首次动画（从 0 → targetValue）
                const state = valueStates[key];
                startAnimation2(state, key);
                return { value: 0 }; // 初始显示 0
            }

            const state = valueStates[key];

            // 2. 后续更新：目标值变化时才触发动画
            if (state.target !== targetValue) {
                state.target = targetValue;
                startAnimation2(state, key); // 从当前值滚动到新目标值
            }

            // 返回当前整数（向下取整）
            return { value: Math.floor(state.current) };
        };

        const startAnimation2 = (state, key) => {
            // 清除之前的计时器（避免冲突）
            if (state.timer) {
                clearInterval(state.timer);
            }

            // 启动动画（每16ms更新一次）
            state.timer = setInterval(() => {
                const diff = state.target - state.current;

                // 差值足够小时，直接到位并停止
                if (Math.abs(diff) < 1) {
                    state.current = state.target;
                    clearInterval(state.timer);
                    state.timer = null;
                } else {
                    // 每次移动差值的 10%（平滑减速）
                    state.current += diff * 0.1;
                }

                // 强制更新响应式（关键：确保Vue检测到变化）
                valueStates[key] = { ...state };
            }, 16);
        };
        // 获取环比值的过渡对象
        const getSubValueOutput = (subValue, type) => {
            if (!type || subValue === undefined || subValue === null || isNaN(Number(subValue))) {
                return { value: 0 };
            }

            const targetValue = Math.floor(Number(subValue));
            const key = `subValueer_${type}`;

            // 1. 首次加载：初始化时 current 设为 0，target 设为目标值（触发从 0 开始的滚动）
            if (!subValueStates[key]) {
                subValueStates[key] = {
                    current: 0, // 首次从 0 开始
                    target: targetValue,
                    timer: null
                };
                // 强制触发首次动画（从 0 → targetValue）
                const state = subValueStates[key];
                startAnimation1(state, key);
                return { value: 0 }; // 初始显示 0
            }

            const state = subValueStates[key];

            // 2. 后续更新：目标值变化时才触发动画
            if (state.target !== targetValue) {
                state.target = targetValue;
                startAnimation1(state, key); // 从当前值滚动到新目标值
            }

            // 返回当前整数（向下取整）
            return { value: Math.floor(state.current) };
        };
        const startAnimation1 = (state, key) => {
            // 清除之前的计时器（避免冲突）
            if (state.timer) {
                clearInterval(state.timer);
            }

            // 启动动画（每16ms更新一次）
            state.timer = setInterval(() => {
                const diff = state.target - state.current;

                // 差值足够小时，直接到位并停止
                if (Math.abs(diff) < 1) {
                    state.current = state.target;
                    clearInterval(state.timer);
                    state.timer = null;
                } else {
                    // 每次移动差值的 10%（平滑减速）
                    state.current += diff * 0.1;
                }

                // 强制更新响应式（关键：确保Vue检测到变化）
                subValueStates[key] = { ...state };
            }, 16);
        };

        const chartData = ref([]);
        const colors = ['#059392', '#2E69B3', '#6EC1ED', '#F7D063', '#EE752A'];
        const loading = ref(true); // 加载状态
        const loading1 = ref(true); // 加载状态
        const loading2 = ref(true); // 加载状态
        const error = ref(null); // 错误状态

        // 颜色映射表
        const colorMap = {
            '#059392': 'green',
            '#2E69B3': 'blue1',
            '#6EC1ED': 'blue',
            '#F7D063': 'yellow',
            '#EE752A': 'orange'
        };

        const reportCategory = ref('')
        const status = ref('')

        // 方法
        const showTModal = (title, index) => {
            isModalT.value = true;
            dtitle.value = title;
            status.value = index
            fetchComplaintDetails()
        };
        // 轮播控制变量
        const currentIndex = ref(0);
        const carouselTimer = ref(null);
        const isCarouselPaused = ref(false);
        const CAROUSEL_INTERVAL = 3000; // 3秒轮播间隔
        const RESUME_DELAY = 3000; // 3秒恢复延迟

        // 当前显示的渠道数据
        const currentChannel = computed(() => {
            return chartData.value[currentIndex.value] || { name: '渠道分布', value: 0 };
        });


        // 轮播到下一项
        const nextChannel = () => {
            if (isCarouselPaused.value) return;

            currentIndex.value = (currentIndex.value + 1) % chartData.value.length;
        };
        // 开始轮播
        const startCarousel = () => {
            // 清除现有计时器
            if (carouselTimer.value) {
                clearInterval(carouselTimer.value);
            }

            // 设置新计时器
            carouselTimer.value = setInterval(nextChannel, CAROUSEL_INTERVAL);
        };

        // 暂停轮播
        const pauseCarousel = () => {
            isCarouselPaused.value = true;
            if (carouselTimer.value) {
                clearInterval(carouselTimer.value);
            }
        };

        // 延迟恢复轮播
        const resumeCarouselWithDelay = () => {
            isCarouselPaused.value = false;
            setTimeout(startCarousel, RESUME_DELAY);
        };

        // 处理项目点击
        const handleItemClick = (item, index) => {
            currentIndex.value = index;
            pauseCarousel();
        };
        // 添加当前查看的渠道响应式变量
        const currentViewChannel = ref({ name: '', value: 0 });

        // 处理查看按钮点击
        const handleViewClick = (channel) => {
            pauseCarousel();
            isModalChannel.value = true;
            // 存储当前点击的渠道信息
            currentViewChannel.value = channel || currentChannel.value;
            fetchSectionDetails();
        };
        // 处理扇区点击
        const handlePieClick = (item) => {
            const index = chartData.value.findIndex(data => data.name === item.name);
            if (index !== -1) {
                currentIndex.value = index;
            }
            pauseCarousel();
        };
        const formatIssueDesc = (desc) => {
            if (!desc) return ''; // 处理空值
            // 限制最多20个字符（中文、英文均按1个字符计算）
            return desc.length > 20 ? desc.slice(0, 20) + '...' : desc;
        };

        // 监听弹窗关闭，恢复轮播
        watch(isModalChannel, (newVal) => {
            if (!newVal) { // 弹窗关闭时
                resumeCarouselWithDelay();
            }
        });
        const showModalChannel = () => {
            isModalChannel.value = true;
            pauseCarousel()
            fetchSectionDetails()
        };

        const setActiveTab = async (index) => {
            try {
                loading1.value = true;
                activeTab.value = index;
                // 等待数据更新后再执行，确保动画触发
                await fetchComplaintData();
                nextTick(() => {
                    // 手动触发重绘（可选，解决个别浏览器不刷新问题）
                    document.querySelector('.order-complaint-bg')?.classList.add('refresh');
                    setTimeout(() => {
                        document.querySelector('.order-complaint-bg')?.classList.remove('refresh');
                    }, 0);
                });
            } catch (err) {
                handleError(err);
            } finally {
                loading1.value = false;
            }
        };


        const getSpan = (index) => {
            if (activeTab.value !== 0 && index <= 3) {
                if (index === 0) return 12;
                if (index === 1 || index === 2) return 6;
                if (index === 3) return 24;
            }
            return 8;
        };


        const handleSizeChange = (newSize) => {
            pageSize.value = newSize;
            currentPage.value = 1;
            fetchComplaintDetails()
        };

        const handleCurrentChange = (newPage) => {
            currentPage.value = newPage;
            fetchComplaintDetails()
        };

        const handleCSizeChange = (newSize) => {
            pageCSize.value = newSize;
            currentCPage.value = 1;
            fetchSectionDetails()
        };

        const handleCCurrentChange = (newPage) => {
            currentCPage.value = newPage;
            fetchSectionDetails()
        };

        const getStatusType = (status) => {
            const statusMap = {
                '满意': 'success',
                '理解': 'primary',
                '不满意': 'danger',
                '待回访': 'danger',
                '处理中': 'primary',
                '处理完成': 'success',
                '处理中（已发单）': 'primary',
                '登记': 'primary',
                '已上报': 'primary',
                '已回访': 'primary',
                '已分派': 'primary',
                '完工审核': 'success',
                '已结案': 'info',
                '暂存': 'info',
                '已核实': 'success',
                '已派遣': 'primary'
            };
            return statusMap[status] || 'default';
        };

        // 优化后的数据处理函数
        const updateComplaintData = (apiData) => {
            if (activeTab.value == 0) {
                complaintData.value[0] = {
                    ...complaintData.value[0],
                    count: apiData.complaintCountCalendarYear
                };

                complaintData.value[2] = {
                    ...complaintData.value[2],
                    count: apiData.doneCountCalendarYear
                };

                complaintData.value[3] = {
                    ...complaintData.value[3],
                    count: apiData.completionRateCalendarYear
                };

                complaintData.value[4] = {
                    ...complaintData.value[4],
                    count: apiData.consultationCountCalendarYear
                };

                complaintData.value[5] = {
                    ...complaintData.value[5],
                    count: apiData.satisfactionCountCalendarYear
                };

                complaintData.value[6] = {
                    ...complaintData.value[6],
                    count: apiData.satisfactionRateCalendarYear
                };
            } else {
                complaintData.value[0] = {
                    ...complaintData.value[0],
                    count: apiData.complaintCountCurrent,
                    subValue: apiData.complaintChain,
                    subValueClass: apiData.complaintChain >= 0 ? 'color-green' : 'color-red'
                };
                complaintData.value[1] = {
                    ...complaintData.value[1],
                    count: apiData.processingCount
                };
                complaintData.value[2] = {
                    ...complaintData.value[2],
                    count: apiData.doneCount
                };
                complaintData.value[4] = {
                    ...complaintData.value[4],
                    count: apiData.consultationCountCurrent,
                    subValue: apiData.consultationChain,
                    subValueClass: apiData.consultationChain >= 0 ? 'color-green' : 'color-red'
                };
            }

        };




        const fetchComplaintDetails = async () => {
            try {
                let data = {};
                if (status.value === 1 || status.value === 2) {
                    data = {
                        dateStr: tabs.value[activeTab.value].english,
                        pageNum: currentPage.value,
                        pageSize: pageSize.value,
                        reportCategory: 2,
                        status: status.value
                    }
                } else if (status.value === 0) {
                    data = {
                        dateStr: tabs.value[activeTab.value].english,
                        pageNum: currentPage.value,
                        pageSize: pageSize.value,
                        reportCategory: 2
                    }
                } else {
                    data = {
                        dateStr: tabs.value[activeTab.value].english,
                        pageNum: currentPage.value,
                        pageSize: pageSize.value,
                        reportCategory: 1,
                    }
                }
                const response = await post('/cloudcall-flowable-api/shenyang-report/screen/suedTransactDetails', data);
                // 添加处理响应数据的代码
                tableData.value = response.data.content || [];
                total.value = response.data.totalElements || tableData.value.length;
            } catch (error) {
                console.error('获取受诉量详情失败', error);
                throw error;
            }
        };

        const fetchSectionDetails = async () => {
            // 使用当前查看的渠道信息，而不是可能过时的chartName
            const reportSection = currentViewChannel.value.name === '96681服务热线' ? 'hotLine96681Count' :
                currentViewChannel.value.name === '省12345' ? 'prov12345MainCount' :
                    currentViewChannel.value.name === '人民网' ? 'peopleOnlineCount' :
                        currentViewChannel.value.name === '市12345' ? 'city12345MainCount' :
                            currentViewChannel.value.name === "'找茬'窗口" ? 'complaintCount' : '';

            try {
                let data = {
                    pageNum: currentCPage.value,
                    pageSize: pageCSize.value,
                    reportSection
                }
                const response = await post('/cloudcall-flowable-api/shenyang-report/screen/reportSectionDetails', data);
                tableChannelData.value = response.data.content || [];
                totalC.value = response.data.totalElements || tableChannelData.value.length;
            } catch (error) {
                console.error('获取受诉量详情失败', error);
                throw error;
            }
        };

        const mapDataToChart = (data) => {
            chartData.value = [
                { name: '96681服务热线', type: 'hotLine96681Count', value: data.hotLine96681Count || 0 },
                { name: '省12345', type: 'prov12345MainCount', value: data.prov12345MainCount || 0 },
                { name: '人民网', type: 'peopleOnlineCount', value: data.peopleOnlineCount || 0 },
                { name: '市12345', type: 'city12345MainCount', value: data.city12345MainCount || 0 },
                { name: "'找茬'窗口", type: 'complaintCount', value: data.complaintCount || 0 },
            ].sort((a, b) => b.value - a.value);
        };


        const chartName = ref('');
        const chartValue = ref('');

        const showName = (index) => {
            chartName.value = chartData.value[index].name;
            chartValue.value = chartData.value[index].value;
        };

        const getBgColorClass = (index) => {
            const color = colors[index % colors.length];
            return colorMap[color] || 'default';
        };

        const handleMouseEnter = () => {
            currentImage.value = hoverImage.value;
        };

        const handleMouseLeave = () => {
            currentImage.value = normalImage.value;
        };

        const handleMouseDown = () => {
            currentImage.value = clickImage.value;
        };

        const handleMouseUp = (event) => {
            const isMouseOver = event.relatedTarget?.closest('.d-t-c') !== null;
            currentImage.value = isMouseOver ? hoverImage.value : normalImage.value;
        };

        const closeD = () => {
            isModalT.value = false;
            isModalChannel.value = false;
            currentImage.value = normalImage.value
            resumeCarouselWithDelay()
        };

        // 错误处理函数
        const handleError = (err) => {
            error.value = err.message || '数据加载失败';
            ElMessage.error(error.value);
        };

        // 图片数据存储
        let darkImageData = null;
        let lightImageData = null;
        let canvasWidth = 400;
        let canvasHeight = 300;

        // 加载图片的函数
        const loadRealImages = () => {
            return new Promise((resolve, reject) => {
                const darkImage = new Image();
                const lightImage = new Image();

                // 解决图片跨域问题
                darkImage.crossOrigin = 'anonymous';
                lightImage.crossOrigin = 'anonymous';

                darkImage.src = bgImagePath;
                lightImage.src = progressImagePath;

                let darkLoaded = false;
                let lightLoaded = false;

                darkImage.onload = () => {
                    darkLoaded = true;
                    if (lightLoaded) resolve({ dark: darkImage, light: lightImage });
                };

                lightImage.onload = () => {
                    lightLoaded = true;
                    if (darkLoaded) resolve({ dark: darkImage, light: lightImage });
                };

                darkImage.onerror = () => reject(new Error('暗色图片加载失败'));
                lightImage.onerror = () => reject(new Error('亮色图片加载失败'));
            });
        }

        const updateTransition = (canvasRef, percent) => {
            if (!canvasRef.value || !darkImageData || !lightImageData) return;

            const ctx = canvasRef.value.getContext('2d');

            // 复制暗色图片数据作为基础
            const resultImageData = new ImageData(
                new Uint8ClampedArray(darkImageData.data),
                darkImageData.width,
                darkImageData.height
            );

            // 计算阈值位置
            const width = resultImageData.width;
            const threshold = width * (percent / 100);

            // 逐像素处理
            for (let y = 0; y < resultImageData.height; y++) {
                for (let x = 0; x < width; x++) {
                    const index = (y * width + x) * 4;

                    // 左侧显示亮色图片，右侧显示暗色图片
                    if (x < threshold) {
                        resultImageData.data[index] = lightImageData.data[index];         // R
                        resultImageData.data[index + 1] = lightImageData.data[index + 1]; // G
                        resultImageData.data[index + 2] = lightImageData.data[index + 2]; // B
                        resultImageData.data[index + 3] = lightImageData.data[index + 3]; // A
                    }
                }
            }

            // 绘制到画布
            ctx.putImageData(resultImageData, 0, 0);
        }
        // 使用 useTransition 创建平滑过渡的输出值
        const completionRateOutput = useTransition(completionRateSource, {
            duration: 1500,
        });

        const validCallRateOutput = useTransition(validCallRateSource, {
            duration: 1500,
        });

        const satisfactionRateOutput = useTransition(satisfactionRateSource, {
            duration: 1500,
        });
        // 初始化默认显示第一个扇面的数据
        const initDefaultData = () => {
            if (chartData.value.length > 0) {
                chartName.value = chartData.value[0].name;
                chartValue.value = chartData.value[0].value;
            }
        };



        watch(
            [() => completionRateOutput.value, () => validCallRateOutput.value, () => satisfactionRateOutput.value],
            () => {
                updateTransition(completionCanvas, completionRateOutput.value);
                updateTransition(validCallCanvas, validCallRateOutput.value);
                updateTransition(satisfactionCanvas, satisfactionRateOutput.value);

            }
        );

        const getWaterlStatisticsData = async () => {
            try {
                const response = await get('/cloudcall-flowable-api/shenyang-report/screen/annualStatistics');
                completionRateSource.value = Number(response.data.completionRate) || 0;
                validCallRateSource.value = Number(response.data.validCallRate) || 0;
                satisfactionRateSource.value = Number(response.data.satisfactionRate) || 0;

            } catch (error) {
                console.error('获取工单服务失败', error);
                throw error;
            }
        };


        const fetchComplaintData = async () => {
            try {
                const data = {
                    dateStr: tabs.value[activeTab.value].english
                };
                const response = await get('/cloudcall-flowable-api/shenyang-report/screen/suedTransact', data);
                updateComplaintData(response.data);

            } catch (error) {
                console.error('获取受诉量失败', error);
                throw error;
            }
        };

        const getReportData = async () => {
            try {
                const response = await get('/cloudcall-flowable-api/shenyang-report/screen/reportSectionCount');
                mapDataToChart(response.data);
                // 重置当前索引，确保轮播动画重新触发
                currentIndex.value = 0;
            } catch (error) {
                console.error('获取渠道分析失败', error);
                throw error;
            }
        };

        // 生命周期钩子
        onMounted(async () => {
            try {
                loading.value = true;
                loading1.value = true;
                loading2.value = true;
                await Promise.all([
                    getWaterlStatisticsData(),
                    fetchComplaintData(),
                    getReportData(),


                ]);
                loadRealImages()
                    .then(images => {
                        // 设置画布尺寸
                        canvasWidth = images.dark.width;
                        canvasHeight = images.dark.height;

                        // 初始化三个画布尺寸
                        [completionCanvas, validCallCanvas, satisfactionCanvas].forEach(canvasRef => {
                            if (canvasRef.value) {
                                canvasRef.value.width = canvasWidth;
                                canvasRef.value.height = canvasHeight;
                            }
                        });

                        // 获取图片数据
                        const tempCanvas = document.createElement('canvas');
                        tempCanvas.width = canvasWidth;
                        tempCanvas.height = canvasHeight;
                        const tempCtx = tempCanvas.getContext('2d');

                        // 绘制暗色图片并获取数据
                        tempCtx.drawImage(images.dark, 0, 0);
                        darkImageData = tempCtx.getImageData(0, 0, canvasWidth, canvasHeight);

                        // 绘制亮色图片并获取数据
                        tempCtx.drawImage(images.light, 0, 0);
                        lightImageData = tempCtx.getImageData(0, 0, canvasWidth, canvasHeight);

                        // 初始化三个画布的显示
                        updateTransition(completionCanvas, completionRateOutput.value);
                        updateTransition(validCallCanvas, validCallRateOutput.value);
                        updateTransition(satisfactionCanvas, satisfactionRateOutput.value);
                    })
                    .catch(error => {
                        console.error('图片加载失败:', error.message);
                        // 错误处理：可以显示默认提示
                        const canvases = [completionCanvas, validCallCanvas, satisfactionCanvas];
                        canvases.forEach(canvasRef => {
                            if (canvasRef.value) {
                                const ctx = canvasRef.value.getContext('2d');
                                ctx.fillStyle = '#f0f0f0';
                                ctx.fillRect(0, 0, canvasWidth, canvasHeight);
                                ctx.fillStyle = '#ff0000';
                                ctx.textAlign = 'center';
                                ctx.textBaseline = 'middle';
                                ctx.fillText('图片加载失败', canvasWidth / 2, canvasHeight / 2);
                            }
                        });
                    });

                initDefaultData()
                // 初始化轮播
                startCarousel();

                // // 修正定时器设置，确保正确执行
                // const refreshInterval1 = setInterval(async () => {
                //     try {
                //         await getWaterlStatisticsData(); // 单独刷新这三个数值，简化排查
                //     } catch (err) {
                //     }

                // }, 5000);

                // // 确保组件卸载时清除定时器
                // onUnmounted(() => {
                //     clearInterval(refreshInterval1);
                // });
                // 每5s刷新一次数据
                const refreshInterval = setInterval(async () => {
                    try {
                        if (activeTab.value == 0) {
                            getReportData()

                        } else {

                            await Promise.all([
                                getWaterlStatisticsData(),
                                fetchComplaintData(),
                                getReportData()
                            ]);
                        }
                    } catch (err) {
                        console.error('定时刷新失败', err);
                    }
                }, 5 * 1000);
                // 组件卸载时清除定时器
                onUnmounted(() => clearInterval(refreshInterval));

            } catch (err) {
                handleError(err);
            } finally {
                loading.value = false;
                loading1.value = false;
                loading2.value = false;
            }
        });

        onUnmounted(() => {

            if (carouselTimer.value) {
                clearInterval(carouselTimer.value);
            }
        })


        // 返回数据和方法
        return {
            isModalT,
            isModalChannel,
            dtitle,
            tabs,
            activeTab,
            complaintData,
            normalImage,
            hoverImage,
            clickImage,
            currentImage,
            tableData,
            tableChannelData,
            currentPage,
            pageSize,
            total,
            currentCPage,
            pageCSize,
            totalC,
            showModalChannel,
            handleCSizeChange,
            handleCCurrentChange,
            initDefaultData,
            completionRateSource,
            completionRateOutput,
            validCallRateSource,
            validCallRateOutput,
            satisfactionRateSource,
            satisfactionRateOutput,
            chartValue,
            chartName,
            showName,
            colors,
            showTModal,
            reportCategory,
            status,
            completionCanvas,
            validCallCanvas,
            satisfactionCanvas,
            setActiveTab,
            getSpan,
            handlePieClick,
            handleSizeChange,
            handleCurrentChange,
            getStatusType,
            getBgColorClass,
            handleMouseEnter,
            handleMouseLeave,
            handleMouseDown,
            handleMouseUp,
            closeD,
            loading,
            loading1,
            loading2,
            error,
            getCountOutput,
            getSubValueOutput,
            getValueOutput,
            chartData,
            currentChannel,
            currentIndex,
            currentViewChannel,
            // valueTransitions,
            formatNumber,
            handleItemClick,
            handleViewClick,
            pauseCarousel,
            resumeCarouselWithDelay,
            getWaterlStatisticsData,
            fetchComplaintData,
            formatIssueDesc,
        };
    }
});
</script>

<style scoped>
/* 添加轮播项激活样式 */
.active-channel {
    /* background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 2px 5px; */
}


/* 工单 */
.order {}

.order-bg {
    background-image: url('../assets/img/huawu.png');
    background-size: 100% 41px;
    margin-top: 77px;
    height: 41px;
}

.order-title {
    height: 21px;
    padding-bottom: 10px;
}

.order-bg img {
    width: 150px;
    height: 52px;
}

/*
.dialog-content {
    height: 330px;
} */

/* 工单信息 */

.order-sub-bg {
    padding: 10px 54px 0;
    /* order-bg-title-bg */
    background-image: url('../assets/img/order-bg.png');
    background-size: 100% 127px;
    /* height: 127px; */
    background-repeat: no-repeat;
    position: relative;
}

.order-sub-title {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, 5px);
    background: linear-gradient(0deg, #DDF9FF 90%, #7BFAFF 0%);
    font-weight: 400;
    color: transparent;
    -webkit-background-clip: text;
    font-family: PingFang SC;
}

.sub-bg,
.sub-bg canvas {
    height: 64px;
    width: 97px;
    position: relative;
}

.sub-bg .Roboto,
:deep(.el-statistic.Roboto .el-statistic__content) {
    font-family: DIN-Bold;
    font-weight: bold;
    font-size: 28px;
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, 0);
    color: transparent;
    background: linear-gradient(0deg, #86cbf0 0%, #ffffff 90%);
    -webkit-background-clip: text;
}

/* 受诉量 */
.complaint-tab>div {
    padding: 3px 10px;
    border-radius: 4px;
    background: #0C234D;
    margin-right: 10px;
    cursor: pointer;
}

.complaint-tab :hover {
    background-color: #3D4F71;
}

.complaint-tab .active {
    border: 1px solid #2D5FEC;
}

.complaint-tab .default {
    border: 1px solid #2C3E59;
}

.common-kongge,
.c-tag {
    width: 2px;
    height: 12px;
    margin-right: 7px;
}

.c-tag {
    background: #549FF9;
    border-radius: 1px;
}

.order-complaint-bg {}

.order-complaint-bg .c-b {
    background-image: url('../assets/img/complainttitle-bg.png');
    background-repeat: no-repeat;
    background-position: bottom;
    padding: 2px 30px;
}

.row-bg .c-bs {
    background-size: contain;
}

.row-bg .c-bs .line {
    width: 1px;
    height: 20px;
    margin-top: 20px;
}

.row-bg .el-col-12 .c-bs {
    background-image: url('../assets/img/complainttitle-bg2.png');
}

.num-right {
    margin-top: 20px;
}

.row-bg .el-col-12 .c-bs>div>div:not(:first-child) {
    flex: 0 0 60%;
}

.row-bg .el-col-12 .c-bs>div>div {
    flex: 0 0 40%;

}

.row-bg .el-col-24 .c-bs>div>div {
    flex: 0 0 50%;
}

.row-bg .el-col-6 .c-bs {
    background-image: url('../assets/img/complainttitle-bg3.png');
}

.row-bg .el-col-24 .c-bs {
    background-image: url('../assets/img/complainttitle-bg4.png');
}

.order-complaint-bg .seats-title,
:deep(.el-statistic.color-red.seats-title) {
    font-family: DIN-Bold;
    font-weight: bolder;
    font-size: 24px;
    /* padding-top: 10px; */
}

.order-complaint-bg>div {
    /* height: 168px; */
}

.c-img {
    width: 74px;
    height: 74px;
    float: left;
}

.order-bg-title {
    font-family: D-DIN;
    font-weight: bold;
    font-size: 32px;
}

.ren {
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 22px;
}

.common-dian {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

/* 加载样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.loading-spinner {
    margin-bottom: 16px;
}

.loading-text {
    color: #909399;
    font-size: 14px;
}

.dialog-bg {
    background-image: url('../assets/img/order-b.png');
    /* width: 1428px; */
}

.d-t {
    height: 83px;
}

:deep(.el-statistic.color-red.seats-title) {
    padding: 0;

}

:deep(.el-statistic .el-statistic__content) {
    filter: drop-shadow(0 1px 2px rgba(14, 22, 51, 0.15)) drop-shadow(0 2px 4px rgba(14, 22, 51, 0.2)) drop-shadow(0 4px 8px rgba(14, 22, 51, 0.25)) drop-shadow(0 8px 16px rgba(14, 22, 51, 0.3)) drop-shadow(0 16px 32px rgba(14, 22, 51, 0.35));

}

:deep(.el-statistic.color-red.seats-title .el-statistic__content) {
    color: #FF6A6A !important;
    font-size: 24px;
    font-family: D-DIN-Bold;
    font-weight: bold;
}

:deep(.el-statistic.color-green.seats-title .el-statistic__content) {
    color: #4EFFAE !important;
    font-size: 24px;
    font-family: D-DIN-Bold;
    font-weight: bold;
}

:deep(.el-statistic.color-white .el-statistic__content) {
    color: #fff;
}
</style>