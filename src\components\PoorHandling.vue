<template>
  <div class="poor-handling">
    <div class="poor-handling-bg common-bg text-left">
      <div class="poor-handling-title common-title padding-left-xll">办理不方</div>
    </div>
    <div class="poor-handling-content">
      <div class="poor-handling-left">
        <el-statistic :value="totalityOutput" group-separator="" class="poor-handling-left-value" />
      </div>
      <div class="poor-handling-right">
        <div class="poor-handling-right-title">
          <div class="poor-handling-right-title-actions">
            <div class="poor-handling-right-title-action" :class="{ active: selectedStar === 0 }"
              @click="handleClick(0)">
              0
            </div>
            <div class="poor-handling-right-title-action" :class="{ active: selectedStar === 1 }"
              @click="handleClick(1)">
              1
            </div>
          </div>
          <div class="poor-handling-right-title-text">星</div>
        </div>
        <div class="poor-handling-list overflow-hidden" ref="scrollContainer">
          <div v-if="displayData.length" class="scroll-wrapper" ref="scrollWrapper">
            <div class="poor-handling-list-item" v-for="(item, index) in displayData" :key="item.id || index"
              @mouseenter="handleHover(index)" @mouseleave="handleMouseLeave(index)">
              <div class="poor-handling-list-item-title">{{ item.name }}</div>
              <div class="poor-handling-list-item-content">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useTransition } from '@vueuse/core'

// 响应式变量
const selectedStar = ref(1);
const totality = ref(0);

const totalityOutput = useTransition(totality, {
  duration: 1500,
});


const scrollContainer = ref(null); // 滚动容器DOM引用
const scrollWrapper = ref(null); // 滚动内容DOM引用
const originalData = ref([]); // 存储原始数据

const displayData = ref([]);  // 用于显示的数据（包含原始数据 + 克隆项用于无缝滚动）（原始数据 + 克隆项，实现无缝滚动）
const currentIndex = ref(0); // 当前滚动到的索引
const timer = ref(null); // 滚动计时器ID
const hoverState = reactive([]); // 记录列表项的hover状态
const isScrolling = ref(true); // 是否正在自动滚动

const itemHeight = ref(0); // 单个列表项的高度
const containerHeight = ref(0); // 滚动容器的高度
const visibleItemCount = 3; // 同时显示的列表项数量
const isTransitioning = ref(false); // 是否正在滚动过渡动画中

// 初始化状态数组
const initStates = () => {
  hoverState.length = 0;
  displayData.value.forEach((_, index) => {
    hoverState[index] = false;
  });
};

// 事件处理
const handleClick = (days) => {
  selectedStar.value = days;
};

const handleHover = (index) => {
  hoverState[index] = true;
  stopScrolling(); // 悬停时停止滚动
};

const handleMouseLeave = (index) => {
  hoverState[index] = false;
  resumeScrolling(); // 离开时恢复滚动
};

// 滚动逻辑
const startScrolling = () => {
  if (timer.value) clearInterval(timer.value);

  const originalLength = originalData.value.length;

  if (originalLength > visibleItemCount) {
    isScrolling.value = true;
    timer.value = setInterval(scrollToNextItem, 3000); // 每3秒滚动一次
  } else {
    isScrolling.value = false;
  }
};

const stopScrolling = () => {
  isScrolling.value = false;
  clearInterval(timer.value);
};

const resumeScrolling = () => {
  isScrolling.value = true;
  startScrolling();
};

const scrollToNextItem = () => {
  if (isTransitioning.value || !isScrolling.value) return;

  if (!scrollWrapper.value || itemHeight.value === 0) return;

  isTransitioning.value = true;
  currentIndex.value++;

  const translateY = -currentIndex.value * itemHeight.value;

  scrollWrapper.value.style.transition = 'transform 0.5s ease-in-out';
  scrollWrapper.value.style.transform = `translateY(${translateY}px)`;

  const originalLength = originalData.value.length;

  // 当滚动到原始数据的末尾时，实现无缝循环
  if (currentIndex.value >= originalLength) {
    setTimeout(() => {
      // 移除过渡动画
      scrollWrapper.value.style.transition = 'none';
      // 瞬间跳回到开头位置
      scrollWrapper.value.style.transform = 'translateY(0)';
      currentIndex.value = 0;
      isTransitioning.value = false;
    }, 500);
  } else {
    setTimeout(() => {
      isTransitioning.value = false;
    }, 500);
  }
};

const calculateDimensions = () => {
  if (scrollWrapper.value && scrollWrapper.value.children.length > 0) {
    const firstChild = scrollWrapper.value.children[0];
    const computedStyle = window.getComputedStyle(firstChild);
    const marginBottom = parseInt(computedStyle.marginBottom) || 23;
    itemHeight.value = firstChild.offsetHeight + marginBottom;

    // 不需要动态设置容器高度，已在CSS中固定设置
    containerHeight.value = itemHeight.value * visibleItemCount;
  }
};

// 设置列表数据
const setListData = (data) => {
  // 先清空现有数据，避免累加
  originalData.value = [...data];
  displayData.value = [];

  // 重新构建显示数据
  displayData.value = [...data];

  // 只有当数据量大于可见项数量时才添加克隆项用于无缝滚动
  if (data.length > visibleItemCount) {
    // 添加前 visibleItemCount 个项目到末尾，用于无缝滚动
    for (let i = 0; i < visibleItemCount; i++) {
      displayData.value.push({ ...data[i], isClone: true });
    }
  }

  currentIndex.value = 0;
  isTransitioning.value = false;

  initStates();
  nextTick(() => {
    calculateDimensions();
    startScrolling();
  });
};

// 获取接口数据
const fetchData = async () => {
  try {
    // 先停止当前滚动，避免状态冲突
    stopScrolling();

    const apiData = [
      { id: 1, name: '浑南区', value: 85 },
      { id: 2, name: '浑南区', value: 84 },
      { id: 3, name: '浑南区', value: 83 },
      { id: 4, name: '浑南区', value: 82 },
      { id: 5, name: '浑南区', value: 81 },
    ];

    setListData(apiData);
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

watch(selectedStar, (newStar) => {
  // 根据不同星级设置不同的值
  const valueMap = {
    0: 4293,
    1: 3850
  };
  totality.value = valueMap[newStar] || 4293;
}, { immediate: true });

// 监听显示数据变化
watch(() => displayData.value, () => {
  initStates();
  nextTick(calculateDimensions);
}, { deep: true });

// 组件挂载时初始化
onMounted(() => {
  fetchData();
  window.addEventListener('resize', calculateDimensions);
});

// 组件卸载时清理
onUnmounted(() => {
  stopScrolling();
  window.removeEventListener('resize', calculateDimensions);
});

</script>
<style scoped>
.poor-handling {
  width: 738px;
}

.poor-handling-bg {
  background-image: url('../../src/assets/img/huawu.png');
  background-size: 100% 41px;
  height: 41px;
  margin-bottom: 11px;
}

.poor-handling-title {
  height: 21px;
  padding-left: 97px;
  padding-bottom: 10px;
}

.poor-handling-content {
  display: flex;
}

.poor-handling-left {
  width: 254px;
  height: 301px;
  margin-left: 31px;
  background-image: url('../../src/assets/img/poorHandling.png');
}

.poor-handling-left-value {
  padding-top: calc(199px - (36px - 26px));
}

.poor-handling-left-value :deep(.el-statistic__content) {
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 36px;
  line-height: 36px;
  color: #FFFFFF;
  text-shadow: 0px 4px 5px rgba(13, 4, 8, 0.2);
  background: linear-gradient(180deg, #8ED2FF 0%, #FFFFFF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.poor-handling-right {
  margin-left: 9px;
}

.poor-handling-right-title {
  height: 36px;
  width: 435px;
  background-image: url('../../src/assets/img/footer-title.png');
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding-bottom: 10px;
}

.poor-handling-right-title-actions {
  display: flex;
  gap: 8px;
}

.poor-handling-right-title-action {
  width: 32px;
  height: 24px;
  background-image: url('../../src/assets/img/switch.png');

  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #6C9CE3;
  line-height: 24px;
  cursor: pointer;
}

.poor-handling-right-title-action.active {
  color: #E3AE3B;
  background-image: url('../../src/assets/img/switch-select.png');
}

.poor-handling-right-title-text {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #6C9CE3;
  line-height: 11px;
}

/* 滚动区域样式 */
.poor-handling-list {
  overflow: hidden;
  /* 隐藏超出容器的内容，实现滚动效果 */
  height: 270px;
}

.scroll-wrapper {
  transition: transform 0.5s ease-in-out;
}

.poor-handling-list-item {
  width: 434px;
  height: 68px;
  background-image: url('../../src/assets/img/unfinished-list-bg.png');
  display: flex;
  line-height: 68px;
  position: relative;
  margin-bottom: 23px;
}

.poor-handling-list-item:last-child {
  margin-bottom: 0;
}

.poor-handling-list-item-title {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 15px;
  color: #FEFEFE;
  position: absolute;
  left: 142px;
}

.poor-handling-list-item-content {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 24px;
  color: #C5983A;
  text-shadow: 0px 4px 5px rgba(13, 4, 8, 0.2);
  position: absolute;
  left: 238px;
  color: #00FECB;
  text-shadow: 0px 4px 5px rgba(13, 4, 8, 0.2);
  background: linear-gradient(180deg, #FF6A6A 0%, #FFFFFF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
