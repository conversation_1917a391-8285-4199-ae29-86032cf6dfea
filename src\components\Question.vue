<template>
    <!-- 问题板块 -->
    <div class="question common">
        <!-- 背景与标题区域 -->
        <div class="question-bg common-bg text-left">
            <div class="question-title common-title padding-left-xll">问题板块</div>
            <img src="../../src/assets/img/question.png" alt="问题板块图标">
        </div>

        <!-- 问题板块区域 -->
        <div class="common-subtitle question-subtitle">
            <!-- 问题板块数据内容 -->
            <div class="seats-bg margin-tb-xs">
                <el-row class="row-bg space-between flex" style="padding-left: 40px;">
                    <!-- 渲染问题类型卡片，点击显示对应弹窗 -->
                    <el-col :span="7" :offline="3" @click="showModal(item)" v-for="(item, index) in seatData"
                        :key="item.type" class="pointer padding-top-xs flex align-center justify-center">

                        <img :src="item.icon" :alt="item.alt">
                        <div class="text-left margin-left-xs" style="flex: 1;">
                            <!-- 数字滚动动画显示问题数量 -->
                            <el-statistic class="Roboto" :value="getCountOutput(item.count, item.type).value">
                                <template #suffix>
                                    <span class="font-size-sm color-54 ren margin-left-xs">件</span>
                                </template>
                            </el-statistic>
                            <div class="color-blue font-size-df">{{ item.title }}</div>

                        </div>
                    </el-col>

                </el-row>

            </div>
        </div>

        <!-- 一级弹窗：显示问题类型下的区域分布 -->
        <div v-if="isModal" class="dialog">
            <div class="dialog-bg">
                <div class="d-t  flex space-between align-center">

                    <img class="d-t-b" src="../assets/img/q-d.png" alt="">
                    <div class="dialog-title common-title">本年-问题板块{{ seatTitle }}</div>
                    <!-- 关闭按钮，包含多种状态图片切换 -->
                    <div class="d-t-c pointer" @click="closeD" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave"
                        @mousedown="handleMouseDown" @mouseup="handleMouseUp">
                        <img :src="currentImage" class="">
                        <!-- 预加载所有状态图片 -->
                        <img v-if="false" :src="normalImage" alt="预加载">
                        <img v-if="false" :src="hoverImage" alt="预加载">
                        <img v-if="false" :src="clickImage" alt="预加载">
                    </div>
                </div>
                <!-- 内容 -->
                <div class="dialog-content margin-top">
                    <el-row class="row-bg " :gutter="20">
                        <!-- 渲染区域分布卡片，点击显示详情表格 -->
                        <el-col :span="5" @click="showTModal(item)" v-for="item in dialogData" :key="item.type"
                            class="flex space-between q-d-b align-center pointer padding-top-xs margin-bottom-sm  col-five"
                            :class="item.type === 'busy' ? 'padding-top-xs' : ''">

                            <div class="text-left q-d-t">
                                <div class="color-blue font-size-df">{{ item.processingUnitName }}</div>
                                <div class="Roboto seats-title">
                                    <span>{{ item.count }}</span>
                                    <span class="font-size-sm color-54 ren margin-left-xs">件</span>
                                </div>

                            </div>
                        </el-col>

                    </el-row>

                </div>
            </div>
        </div>

        <!-- 二级弹窗：显示区域详情表格 -->
        <div v-if="isModalT" class="dialog">
            <div class="dialog-bg d-t-m" style="height: 812px;max-height: 812px;">
                <div class="d-t  flex space-between align-center">

                    <img class="d-t-b" src="../assets/img/q-d.png" alt="">
                    <div class="dialog-title common-title">本年-问题板块{{ seatTitle }}</div>
                    <!-- 关闭按钮 -->
                    <div class="d-t-c pointer" @click="closeD" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave"
                        @mousedown="handleMouseDown" @mouseup="handleMouseUp">
                        <img :src="currentImage" class="">
                        <!-- 预加载所有状态图片 -->
                        <img v-if="false" :src="normalImage" alt="预加载">
                        <img v-if="false" :src="hoverImage" alt="预加载">
                        <img v-if="false" :src="clickImage" alt="预加载">
                    </div>
                </div>
                <!-- 内容 -->
                <div class="dialog-content">
                    <div class="d-n flex align-center space-between">
                        <div class="common-title q-d-name">{{ seatlName }}</div>
                        <div class="font-size-sm po color-white">
                            <div class="common-btn  check-more" @click="back">
                                返回
                            </div>
                        </div>
                    </div>
                    <!-- 问题详情表格 -->
                    <div class="complaint-table-container">
                        <el-table :data="tableData" stripe highlight-current-row height="610px" style="width: 100%">
                            <el-table-column type="index" label="序号" width="60"></el-table-column>

                            <el-table-column prop="dispatchStatusName" label="状态" width="150">
                                <template #default="scope">
                                    <!-- 根据状态显示不同颜色标签 -->
                                    <el-text :type="getStatusType(scope.row.dispatchStatusName)">{{
                                        scope.row.dispatchStatusName }}</el-text>
                                </template>
                            </el-table-column>

                            <el-table-column prop="billNo" label="客诉单号" width="150"></el-table-column>

                            <el-table-column prop="occurAddress" label="发生地址" width="150"></el-table-column>

                            <el-table-column prop="issueSectionName" label="问题板块" width="120"></el-table-column>

                            <el-table-column prop="reportTypeName" label="反映类型" width="120"></el-table-column>

                            <el-table-column prop="reflectionSourceName" label="反映来源" width="120"></el-table-column>

                            <el-table-column prop="issueReasonName" label="问题原因" width="200">
                                <template #default="scope">
                                    <!-- 长文本使用tooltip显示完整内容 -->
                                    <el-tooltip v-if="scope.row.issueReasonName" :content="scope.row.issueReasonName"
                                        :disabled="scope.row.issueReasonName.length <= 20" placement="top"
                                        effect="customized" :open-delay="300">
                                        <!-- 显示的内容（截断+2行） -->
                                        <div class="issue-description" v-if="scope.row.issueReasonName.length >= 20">
                                            {{ formatIssueDesc(scope.row.issueReasonName) }}
                                        </div>
                                        <div v-else>{{ scope.row.issueReasonName }}</div>

                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column prop="processingUnitName" label="处理单位" width="150"></el-table-column>

                            <el-table-column prop="callNumber" label="来电号码" width="130"></el-table-column>

                            <el-table-column prop="contactNumber" label="联系电话" width="130"></el-table-column>

                            <el-table-column prop="acceptanceOpinion" label="回访结果" width="200">
                                <template #default="scope">
                                    <!-- 长文本使用tooltip显示完整内容 -->
                                    <el-tooltip v-if="scope.row.acceptanceOpinion" :content="scope.row.acceptanceOpinion"
                                        :disabled="scope.row.acceptanceOpinion.length <= 20" placement="top"
                                        effect="customized" :open-delay="300">
                                        <!-- 显示的内容（截断+2行） -->
                                        <div class="issue-description" v-if="scope.row.acceptanceOpinion.length >= 20">
                                            {{ formatIssueDesc(scope.row.acceptanceOpinion) }}
                                        </div>
                                        <div v-else>{{ scope.row.acceptanceOpinion }}</div>

                                    </el-tooltip>
                                </template>
                            </el-table-column>

                            <el-table-column prop="reflectionSourceName" label="回访方式" width="120">

                            </el-table-column>
                        </el-table>
                        <!-- 分页控制 -->
                        <el-pagination class="margin-top-xs" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" :current-page="currentPage" :page-size="pageSize"
                            layout="total,->,  pager,sizes, jumper,>-" :total="total">
                        </el-pagination>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onBeforeUnmount, onUnmounted } from 'vue';
import { get, post } from '../utils/request';
import { useTransition } from '@vueuse/core'

// 导入各种图标资源
import normalImg from '../assets/img/close-btn.png'
import hoverImg from '../assets/img/close-hover.png'
import clickImg from '../assets/img/close-click.png'
import q1 from '../assets/img/q1.png'
import q2 from '../assets/img/q2.png'
import q3 from '../assets/img/q3.png'
import q4 from '../assets/img/q4.png'
import q5 from '../assets/img/q5.png'
import q6 from '../assets/img/q6.png'

export default {
    name: 'Question',
    components: {
        // Modal
    },

    setup() {
        let intervalId; // 定时器ID，用于定时刷新数据
        // 存储动态创建的过渡对象
        // const countTransitions = new Map();
        const countStates = reactive({}); // 存储数字滚动动画状态

        // 弹窗控制状态
        const isModal = ref(false)
        const isModalT = ref(false)

        // 当前选中的问题类型和区域信息
        const seatType = ref('')
        const seatTitle = ref('')
        const seatlName = ref('')
        const processingUnit = ref('')

        // 显示一级弹窗（问题类型下的区域分布）
        const showModal = (item) => {
            isModalT.value = false
            isModal.value = true
            seatType.value = item.type
            console.log('seatType', seatType.value)
            seatTitle.value = item.title
            getAreaCount() // 获取区域数据
        }

        // 从二级弹窗返回一级弹窗
        const back = () => {
            isModalT.value = false
            isModal.value = true
        }

        // 显示二级弹窗（区域详情表格）
        const showTModal = (item) => {
            isModalT.value = true
            isModal.value = false

            seatlName.value = item.processingUnitName
            processingUnit.value = item.processingUnit
            getAreaDetails() // 获取详情数据
        }

        // 问题板块数据配置
        const seatData = ref([])

        // 初始化表格数据
        const tableData = ref([]);

        // 分页控制
        const currentPage = ref(1);
        const pageSize = ref(10);
        const total = ref();

        // 数字滚动动画函数
        const getCountOutput = (count, type) => {
            if (!type || count === undefined || count === null || isNaN(Number(count))) {
                return { value: 0 };
            }

            const targetValue = Math.floor(Number(count));
            const key = `counter_${type}`;

            // 1. 首次加载：初始化时 current 设为 0，target 设为目标值（触发从 0 开始的滚动）
            if (!countStates[key]) {
                countStates[key] = {
                    current: 0, // 首次从 0 开始
                    target: targetValue,
                    timer: null
                };
                // 强制触发首次动画（从 0 → targetValue）
                const state = countStates[key];
                startAnimation(state, key);
                return { value: 0 }; // 初始显示 0
            }

            const state = countStates[key];

            // 2. 后续更新：目标值变化时才触发动画
            if (state.target !== targetValue) {
                state.target = targetValue;
                startAnimation(state, key); // 从当前值滚动到新目标值
            }

            // 返回当前整数（向下取整）
            return { value: Math.floor(state.current) };
        };

        // 抽离动画逻辑为单独函数（复用）
        const startAnimation = (state, key) => {
            // 清除之前的计时器（避免冲突）
            if (state.timer) {
                clearInterval(state.timer);
            }

            // 启动动画（每16ms更新一次）
            state.timer = setInterval(() => {
                const diff = state.target - state.current;

                // 差值足够小时，直接到位并停止
                if (Math.abs(diff) < 1) {
                    state.current = state.target;
                    clearInterval(state.timer);
                    state.timer = null;
                } else {
                    // 每次移动差值的 10%（平滑减速）
                    state.current += diff * 0.1;
                }

                // 强制更新响应式（关键：确保Vue检测到变化）
                countStates[key] = { ...state };
            }, 16);
        };

        // 根据状态名称获取对应的Element UI文本类型
        const getStatusType = (status) => {
            const statusMap = {
                '满意': 'success',
                '理解': 'primary',
                '不满意': 'danger',
                '待回访': 'danger',
                '处理中': 'primary',
                '处理完成': 'success',
                '处理中（已发单）': 'primary',
                '登记': 'primary',
                '已上报': 'primary',
                '已回访': 'primary',
                '已分派': 'primary',
                '完工审核': 'success',
                '已结案': 'info',
                '暂存': 'info',
                '已核实': 'success',
                '已派遣': 'primary'
            };
            return statusMap[status] || 'default';
        };

        // 分页事件处理
        const handleSizeChange = (newSize) => {
            pageSize.value = newSize;
            currentPage.value = 1; // 重置到第一页
            getAreaDetails() // 重新获取数据
        };

        const handleCurrentChange = (newPage) => {
            currentPage.value = newPage;
            getAreaDetails() // 重新获取数据
        };

        // 弹框关闭按钮状态管理
        const normalImage = ref(normalImg)
        const hoverImage = ref(hoverImg)
        const clickImage = ref(clickImg)
        const currentImage = ref(normalImg)

        // 问题板块数据配置
        const dialogData = ref([])

        // 定义关闭按钮事件处理方法
        const handleMouseEnter = () => {
            currentImage.value = hoverImage.value
        }

        const handleMouseLeave = () => {
            currentImage.value = normalImage.value
        }

        const handleMouseDown = () => {
            currentImage.value = clickImage.value
        }

        const handleMouseUp = (event) => {
            const isMouseOver = event.relatedTarget?.closest('.d-t-c') !== null
            currentImage.value = isMouseOver ? hoverImage.value : normalImage.value
        }

        // 关闭弹窗
        const closeD = () => {
            isModal.value = false
            isModalT.value = false
            currentImage.value = normalImage.value
        }

        // 映射API数据到组件格式
        const mapDataToChart = (data) => {
            seatData.value = [
                { title: '营业', type: 'BUSINESS', count: data.BUSINESS, icon: q1 },
                { title: '供水', type: 'WATER_SUPPLY', count: data.WATER_SUPPLY, icon: q2 },
                { title: '管网', type: 'PIPE_NETWORK', count: data.PIPE_NETWORK, icon: q3, },
                { title: '排水', type: 'DRAINAGE', count: data.DRAINAGE, icon: q4, },
                { title: '工程', type: 'ENGINEERING', count: data.ENGINEERING, icon: q5, },
                { title: '服务', type: 'SERVICE', count: data.SERVICE, icon: q6, },
            ]
        }

        // 获取问题板块件数
        const getSectionCount = async () => {
            try {
                const response = await get('/cloudcall-flowable-api/shenyang-report/screen/questionSectionCount');
                mapDataToChart(response.data)
            } catch (error) {
                console.error('获取问题板块件数失败', error);
            }
        };

        // 获取问题板块类型分区件数统计
        const getAreaCount = async () => {
            try {
                const data = {
                    issueSection: seatType.value
                }
                const response = await get('/cloudcall-flowable-api/shenyang-report/screen/questionSectionAreaCount', data);
                dialogData.value = response.data

            } catch (error) {
                console.error('获取问题板块类型分区件数统计失败', error);
            }
        };

        // 获取问题板块类型分区详情列表
        const getAreaDetails = async () => {
            try {
                const data = {
                    issueSection: seatType.value,
                    pageNum: currentPage.value,
                    pageSize: pageSize.value,
                    processingUnit: processingUnit.value
                }
                const response = await post('/cloudcall-flowable-api/shenyang-report/screen/questionSectionAreaDetails', data);
                tableData.value = response.data.content
                total.value = response.data.totalElements

            } catch (error) {
                console.error('获取问题板块类型分区详情列表失败', error);
            }
        };

        // 组件挂载时初始化数据并启动定时器
        onMounted(() => {
            getSectionCount()
            intervalId = setInterval(getSectionCount, 5000); // 每5秒刷新一次数据
        });

        // 组件卸载时清理定时器
        onUnmounted(() => {
            clearInterval(intervalId);
        })

        // 格式化长文本描述，限制显示长度
        const formatIssueDesc = (desc) => {
            if (!desc) return ''; // 处理空值
            // 限制最多20个字符（中文、英文均按1个字符计算）
            return desc.length > 20 ? desc.slice(0, 20) + '...' : desc;
        };

        // 暴露组件方法和数据
        return {
            seatData,
            getSectionCount,
            // 弹框
            showTModal,
            getAreaCount,
            getAreaDetails,
            seatType,
            seatTitle,
            seatlName,
            processingUnit,
            showModal,
            back,
            isModal,
            isModalT,
            dialogData,
            currentImage,
            normalImage,
            hoverImage,
            clickImage,
            tableData,
            currentPage,
            pageSize,
            total,
            getStatusType,
            handleSizeChange,
            handleCurrentChange,
            handleMouseEnter,
            handleMouseLeave,
            handleMouseDown,
            handleMouseUp,
            closeD,
            getCountOutput,
            formatIssueDesc
        };
    }
}
</script>

<style scoped>
/* 话务 */
.question {}

.question-bg {
    background-image: url('../../src/assets/img/huawu.png');
    background-size: 100% 41px;
    margin-top: 20px;
    height: 41px;
}

.question-title {
    height: 21px;
    padding-bottom: 10px;
}

.question-bg img {
    width: 152px;
    height: 52px;
}

/* 问题板块 */
.question-subtitle {}

.row-bg>div {
    cursor: pointer;
}

.seats-bg img {
    height: 84px;
    width: 88px;
}

.seats-title {
    font-family: DIN-Bold;
    font-weight: bold;
    font-size: 28px;
}

.dialog-content .seats-title span:first-child {
    color: #F4FDFF !important;
    opacity: 0.5;
    font-family: DIN-Bold;
    font-weight: bold;
}

.ren {
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 22px;
}

/* 问题板块人数 按钮 */
.check-more {
    background-image: url('../../src/assets/img/check-btn.png');
}

.check-more:hover {
    background-image: url('../../src/assets/img/hover-btn.png');
}

.check-more:link {
    background-image: url('../../src/assets/img/click-btn.png');
}

.dialog-bg {
    background-image: url('../assets/img/q-d-b.png');
    width: 1348px;
    height: 665px;
}

.dialog-bg.d-t-m {
    background-image: url('../assets/img/q-t-m.png');
    width: auto;
    /* width: 1529px; */
}

/* 设置数字滚动的样式，使用渐变和阴影效果 */
:deep(.el-statistic.Roboto .el-statistic__content) {
    font-family: DIN-Bold;
    font-weight: bold;
    font-size: 28px;
    color: transparent;
    background: linear-gradient(0deg, #86cbf0 0%, #ffffff 90%);
    -webkit-background-clip: text;
    filter: drop-shadow(0 1px 2px rgba(14, 22, 51, 0.15)) drop-shadow(0 2px 4px rgba(14, 22, 51, 0.2)) drop-shadow(0 4px 8px rgba(14, 22, 51, 0.25)) drop-shadow(0 8px 16px rgba(14, 22, 51, 0.3)) drop-shadow(0 16px 32px rgba(14, 22, 51, 0.35));
}
</style>