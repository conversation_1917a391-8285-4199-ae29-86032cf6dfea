<template>
  <div class="timeout-ranking">
    <div class="timeout-ranking-title">
      <div class="timeout-ranking-title-icon"></div>
      <div class="timeout-ranking-title-text">超时件排名</div>
    </div>
    <div class="timeout-ranking-totality">
      <div class="timeout-ranking-totality-bg">
        <el-statistic :value="totalityOutput" group-separator="" class="timeout-ranking-totality-value" />
      </div>
    </div>
    <div class="timeout-ranking-list">

    </div>
  </div>
</template>
<script setup>

import { ref } from 'vue';
import { useTransition } from '@vueuse/core';

const totality = ref(105);
const totalityOutput = useTransition(totality, {
  duration: 1500,
});

</script>
<style scoped>
.timeout-ranking {
  width: 751px;
}

.timeout-ranking-title {
  display: flex;
  align-items: center;
  border-bottom: 1px;
}

.timeout-ranking-title-icon {
  background-image: url('../../src/assets/img/timeout-ranking-titleicon.png');
  width: 19px;
  height: 20px;
  margin-right: 8px;
}

.timeout-ranking-title-text {
  width: 82px;
  height: 16px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #F9FAFA;
  line-height: 11px;
}

.timeout-ranking-totality {
  width: 100%;
  text-align: center;
}

.timeout-ranking-totality-bg {
  background-image: url('../../src/assets/img/timeout-ranking-rank.png');
  width: 719px;
  height: 70px;
  margin: 0 auto;
}

.timeout-ranking-totality-value {
  padding-top: 11px;
}

.timeout-ranking-totality-value :deep(.el-statistic__content) {
  font-family: D-DIN;
  font-weight: bold;
  font-size: 36px;
  color: #FFFFFF;
  line-height: 24px;
  background: linear-gradient(0deg, #FFFFFF 0%, #9AD1EE 87.4267578125%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
