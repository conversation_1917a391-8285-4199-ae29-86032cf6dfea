<template>
  <div class="timeout-ranking">
    <div class="timeout-ranking-title">
      <div class="timeout-ranking-title-icon"></div>
      <div class="timeout-ranking-title-text">超时件排名</div>
    </div>
    <div class="timeout-ranking-totality">
      <div class="timeout-ranking-totality-bg">
        <el-statistic :value="totalityOutput" group-separator="" class="timeout-ranking-totality-value" />
      </div>
    </div>
    <div class="timeout-ranking-list">
      <!-- 固定表头 -->
      <div class="timeout-ranking-header">
        <div class="timeout-ranking-header-cell company">分公司</div>
        <div class="timeout-ranking-header-cell period-new">本期新增</div>
        <div class="timeout-ranking-header-cell period-total">本期办结</div>
        <div class="timeout-ranking-header-cell timeout-count">超时件数</div>
      </div>
      <!-- 滚动数据区域 -->
      <div class="timeout-ranking-content overflow-hidden" ref="scrollContainer">
        <div v-if="displayData.length" class="scroll-wrapper" ref="scrollWrapper">
          <div class="timeout-ranking-row" v-for="(item, index) in displayData"
            :key="item.isClone ? `clone-${item.id}-${index}` : `${item.id}-${index}`"
            @mouseenter="handleHover(index)"
            @mouseleave="handleMouseLeave(index)">
            <div class="timeout-ranking-cell company">{{ item.company }}</div>
            <div class="timeout-ranking-cell period-new">{{ item.periodNew }}</div>
            <div class="timeout-ranking-cell period-total">{{ item.periodTotal }}</div>
            <div class="timeout-ranking-cell timeout-count">{{ item.timeoutCount }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useTransition } from '@vueuse/core';

// 响应式变量
const totality = ref(105);
const totalityOutput = useTransition(totality, {
  duration: 1500,
});

// 滚动相关变量
const scrollContainer = ref(null); // 滚动容器DOM引用
const scrollWrapper = ref(null); // 滚动内容DOM引用
const originalData = ref([]); // 存储原始数据
const displayData = ref([]);  // 用于显示的数据（包含原始数据 + 克隆项用于无缝滚动）
const currentIndex = ref(0); // 当前滚动到的索引
const timer = ref(null); // 滚动计时器ID
const hoverState = reactive([]); // 记录列表项的hover状态
const isScrolling = ref(true); // 是否正在自动滚动

const itemHeight = ref(0); // 单个列表项的高度
const containerHeight = ref(0); // 滚动容器的高度
const visibleItemCount = 4; // 同时显示的列表项数量
const isTransitioning = ref(false); // 是否正在滚动过渡动画中

// 初始化状态数组
const initStates = () => {
  hoverState.length = 0;
  displayData.value.forEach((_, index) => {
    hoverState[index] = false;
  });
};

// 事件处理
const handleHover = (index) => {
  hoverState[index] = true;
  stopScrolling(); // 悬停时停止滚动
};

const handleMouseLeave = (index) => {
  hoverState[index] = false;
  resumeScrolling(); // 离开时恢复滚动
};

// 滚动逻辑
const startScrolling = () => {
  if (timer.value) clearInterval(timer.value);

  const originalLength = originalData.value.length;

  if (originalLength > visibleItemCount) {
    isScrolling.value = true;
    timer.value = setInterval(scrollToNextItem, 3000); // 每3秒滚动一次
  } else {
    isScrolling.value = false;
  }
};

const stopScrolling = () => {
  isScrolling.value = false;
  clearInterval(timer.value);
};

const resumeScrolling = () => {
  isScrolling.value = true;
  startScrolling();
};

const scrollToNextItem = () => {
  if (isTransitioning.value || !isScrolling.value) return;

  if (!scrollWrapper.value || itemHeight.value === 0) return;

  isTransitioning.value = true;
  currentIndex.value++;

  const translateY = -currentIndex.value * itemHeight.value;

  scrollWrapper.value.style.transition = 'transform 0.5s ease-in-out';
  scrollWrapper.value.style.transform = `translateY(${translateY}px)`;

  const originalLength = originalData.value.length;

  if (currentIndex.value >= originalLength) {
    setTimeout(() => {
      scrollWrapper.value.style.transition = 'none';
      scrollWrapper.value.style.transform = 'translateY(0)';
      currentIndex.value = 0;
      isTransitioning.value = false;
    }, 500);
  } else {
    setTimeout(() => {
      isTransitioning.value = false;
    }, 500);
  }
};

const calculateDimensions = () => {
  if (scrollWrapper.value && scrollWrapper.value.children.length > 0) {
    const firstChild = scrollWrapper.value.children[0];
    const computedStyle = window.getComputedStyle(firstChild);
    const marginBottom = parseInt(computedStyle.marginBottom) || 0;
    itemHeight.value = firstChild.offsetHeight + marginBottom;

    containerHeight.value = itemHeight.value * visibleItemCount;
  }
};

// 设置列表数据
const setListData = (data) => {
  originalData.value = [...data];
  displayData.value = [];

  displayData.value = [...data];

  // 只有当数据量大于可见项数量时才添加克隆项用于无缝滚动
  if (data.length > visibleItemCount) {
    // 添加前 visibleItemCount 个项目到末尾，用于无缝滚动
    for (let i = 0; i < visibleItemCount; i++) {
      displayData.value.push({ ...data[i], isClone: true });
    }
  }

  currentIndex.value = 0;
  isTransitioning.value = false;

  initStates();
  nextTick(() => {
    calculateDimensions();
    startScrolling();
  });
};

// 获取接口数据
const fetchData = async () => {
  try {
    // 先停止当前滚动，避免状态冲突
    stopScrolling();

    // 临时模拟数据（替换为你的接口调用）
    const apiData = [
      { id: 1, company: '铁西', periodNew: 11, periodTotal: 22, timeoutCount: 33 },
      { id: 2, company: '皇姑', periodNew: 11, periodTotal: 22, timeoutCount: 33 },
      { id: 3, company: '浑南', periodNew: 11, periodTotal: 22, timeoutCount: 33 },
      { id: 4, company: '于洪', periodNew: 11, periodTotal: 22, timeoutCount: 33 },
      { id: 5, company: '沈北', periodNew: 11, periodTotal: 22, timeoutCount: 33 },
      { id: 6, company: '苏家屯', periodNew: 11, periodTotal: 22, timeoutCount: 33 },
    ];

    setListData(apiData);
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

// 监听显示数据变化
watch(() => displayData.value, () => {
  initStates();
  nextTick(calculateDimensions);
}, { deep: true });

// 组件挂载时初始化
onMounted(() => {
  fetchData();
  window.addEventListener('resize', calculateDimensions);
});

// 组件卸载时清理
onUnmounted(() => {
  stopScrolling();
  window.removeEventListener('resize', calculateDimensions);
});

</script>
<style scoped>
.timeout-ranking {
  width: 751px;
}

.timeout-ranking-title {
  display: flex;
  align-items: center;
  border-bottom: 1px;
}

.timeout-ranking-title-icon {
  background-image: url('../../src/assets/img/timeout-ranking-titleicon.png');
  width: 19px;
  height: 20px;
  margin-right: 8px;
}

.timeout-ranking-title-text {
  width: 82px;
  height: 16px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #F9FAFA;
  line-height: 11px;
}

.timeout-ranking-totality {
  width: 100%;
  text-align: center;
}

.timeout-ranking-totality-bg {
  background-image: url('../../src/assets/img/timeout-ranking-rank.png');
  width: 719px;
  height: 70px;
  margin: 0 auto;
}

.timeout-ranking-totality-value {
  padding-top: 11px;
}

.timeout-ranking-totality-value :deep(.el-statistic__content) {
  font-family: D-DIN;
  font-weight: bold;
  font-size: 36px;
  color: #FFFFFF;
  line-height: 24px;
  background: linear-gradient(0deg, #FFFFFF 0%, #9AD1EE 87.4267578125%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 表格列表样式 */
.timeout-ranking-list {
  width: 719px;
  margin: 16px auto 0;
  background: linear-gradient(180deg, rgba(0, 50, 100, 0.8) 0%, rgba(0, 30, 60, 0.6) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

/* 固定表头 */
.timeout-ranking-header {
  display: flex;
  height: 40px;
  line-height: 40px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.timeout-ranking-header-cell {
  text-align: center;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #F9FAFA;
}

.timeout-ranking-header-cell.company {
  width: 25%;
}

.timeout-ranking-header-cell.period-new {
  width: 25%;
}

.timeout-ranking-header-cell.period-total {
  width: 25%;
}

.timeout-ranking-header-cell.timeout-count {
  width: 25%;
}

/* 滚动内容区域 */
.timeout-ranking-content {
  overflow: hidden;
  height: 160px; /* 显示4行数据，每行40px */
}

.scroll-wrapper {
  transition: transform 0.5s ease-in-out;
}

/* 数据行样式 */
.timeout-ranking-row {
  display: flex;
  height: 40px;
  line-height: 40px;
  background-image: url('../../src/assets/img/timeout-ranking-list-tr.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.timeout-ranking-cell {
  text-align: center;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

.timeout-ranking-cell.company {
  width: 25%;
}

.timeout-ranking-cell.period-new {
  width: 25%;
}

.timeout-ranking-cell.period-total {
  width: 25%;
}

.timeout-ranking-cell.timeout-count {
  width: 25%;
  color: #FFD700; /* 超时件数用金色突出显示 */
  font-weight: bold;
}

/* 悬停效果 */
.timeout-ranking-row:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
</style>
