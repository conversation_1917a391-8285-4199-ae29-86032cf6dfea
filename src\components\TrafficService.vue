<template>
  <!-- 话务服务 -->
  <div class="traffic common">
    <!-- 背景与标题区域 -->
    <div class="traffic-bg common-bg text-left">
      <div class="traffic-title common-title padding-left-xll">话务服务</div>
      <img src="../../src/assets/img/hw-service.png" alt="话务服务图标">
    </div>

    <!-- 实时坐席区域 -->
    <div class="common-subtitle traffic-subtitle">
      <div class="flex nowarp margin-tb-sm">
        <img src="../../src/assets/img/icon.png" alt="图标">
        <div class="margin-left-xs">实时坐席</div>
      </div>
      <div class="liness"></div>

      <!-- 坐席数据内容 -->
      <div class="seats-bg margin-tb-xs">
        <div class="common-layout">
          <el-container>
            <!-- 左侧数据 -->
            <el-aside width="200px">
              <div @click="showModal" class="flex space-between align-center pointer">
                <img src="../../src/assets/img/seats.png">
                <div class="text-right">
                  <!-- 1. 模板中使用过渡后的值（transitions.xxx.value） -->
                  <el-statistic class="seats-title" :value="agentOnCallOutput">
                    <template #suffix>
                      <span class="font-size-sm color-54 ren margin-left-xs">人</span>
                    </template>
                  </el-statistic>
                  <div class="color-blue font-size-df">通话坐席数</div>
                </div>
              </div>
              <div @click="showModal" class="flex space-between align-center pointer padding-top-xs">
                <img src="../../src/assets/img/busy.png">
                <div class="text-right">
                  <el-statistic class="seats-title" :value="agentBusyOutput">
                    <template #suffix>
                      <span class="font-size-sm color-54 ren margin-left-xs">人</span>
                    </template>
                  </el-statistic>
                  <div class="color-blue font-size-df">示忙坐席数</div>
                </div>
              </div>
            </el-aside>

            <!-- 中间数据 -->
            <el-main>
              <div class="margin-top-xs">
                <div class="color-blue font-size">坐席人数</div>
                <el-statistic :value-style="{
                  fontSize: '40px'
                }" class="seats-title" :value="agentCountOutput">

                </el-statistic>
                <!-- 按钮 -->
                <div class="font-size-sm color-white">
                  <div :class="['common-btn', 'check-more', hoverClass, clickClass]" @mouseenter="handleHover"
                    @mouseleave="handleMouseLeave" @click="handleClick">
                    查看更多
                  </div>
                </div>
              </div>
            </el-main>

            <!-- 右侧数据 -->
            <el-aside width="200px">
              <div @click="showModal" class="flex space-between align-center pointer">
                <div class="text-left">
                  <el-statistic class="seats-title " :value="agentIdleOutput">
                    <template #suffix>
                      <span class="font-size-sm color-54 ren margin-left-xs">人</span>
                    </template>
                  </el-statistic>
                  <div class="color-blue font-size-df">示闲坐席数</div>
                </div>
                <img src="../../src/assets/img/leisure.png">
              </div>
              <div @click="showModal" class="flex space-between align-center pointer padding-top-xs">
                <div class="text-left">
                  <el-statistic class="seats-title " :value="agentOfflineOutput">
                    <template #suffix>
                      <span class="font-size-sm color-54 ren margin-left-xs">人</span>
                    </template>
                  </el-statistic>
                  <div class="color-blue font-size-df">离线坐席数</div>
                </div>
                <img src="../../src/assets/img/offline.png">
              </div>
            </el-aside>
          </el-container>
        </div>
      </div>
    </div>


    <!-- 今日话务区域 -->
    <div class="common-subtitle">
      <div class="flex nowarp margin-bottom-xs ">
        <img src="../../src/assets/img/icon.png" alt="图标">
        <div class="margin-left-xs">今日话务</div>
      </div>
      <div class="liness"></div>

      <!-- 内容 -->
      <div class="margin-top-xs">
        <div v-for="(item, index) in trafficData" @click="showTModal(item.title, index)" :key="index"
          class="num-calls pointer font-size-df margin-top-xs" :class="trafficIndex == index ? 'active' : ''">
          <el-row>
            <el-col :span="12">
              <div class="flex align-center grid-content ep-bg-purple">
                <img class="num-img" :src="item.icon" :alt="item.title">
                <div class="flex align-center space-between padding-lr-sm num-subtitle">
                  <div class="color-blue">{{ item.title }}</div>
                  <el-statistic class="seats-title " :value="getValueOutput(item.value, item.type).value">
                    <template #suffix>
                      <span class="font-size-sm color-54 ren margin-left-xs">{{ item.unit }}</span>
                    </template>
                  </el-statistic>
                </div>
              </div>
            </el-col>

            <div class="line" :class="index === 3 || index === 5 ? 'bg-tr' : ''"></div>

            <el-col :span="12" v-if="item.subtitle">
              <div class="flex align-center num-right grid-content ep-bg-purple padding-left-lg">
                <div class="color-blue">{{ item.subtitle }}：</div>
                <el-statistic :class="item.subValue >= 0 ? 'color-green' : 'color-red'" class="seats-title"
                  :value="getSubValueOutput(item.subValue, item.type).value">
                  <template #suffix>
                    <span class="font-size-sm color-54 ren margin-left-xs">{{ item.subUnit }}</span>
                  </template>
                </el-statistic>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <!-- 坐席人数弹窗 -->
    <div v-if="isModal" class="dialog">
      <div class="dialog-bg">
        <div class="d-t  flex space-between align-center">

          <img class="d-t-b" src="../../src/assets/img/hw-d.png" alt="">
          <div class="dialog-title common-title">坐席人数</div>
          <div class="d-t-c pointer" @click="closeD" @mouseenter="handleMouseEnter" @mouseleave="handleTMouseLeave"
            @mousedown="handleMouseDown" @mouseup="handleMouseUp">
            <img :src="currentImage" class="">
            <!-- 预加载所有状态图片 -->
            <img v-if="false" :src="normalImage" alt="预加载">
            <img v-if="false" :src="hoverImage" alt="预加载">
            <img v-if="false" :src="clickImage" alt="预加载">
          </div>
        </div>
        <!-- 内容 -->
        <div class="dialog-content">

          <div class="complaint-table-container">
            <el-table :data="hwData" stripe highlight-current-row fit style="width: 100%;height: 486px;">
              <el-table-column type="index" label="序号" width="160"></el-table-column>
              <el-table-column prop="agentJobnumber" label="坐席工号" width="200"></el-table-column>
              <el-table-column prop="agentName" label="姓名" width="160"></el-table-column>
              <el-table-column prop="extension" label="分机号" width="160"></el-table-column>
              <el-table-column prop="statusDuration" label="持续时间" width="160"></el-table-column>
              <el-table-column prop="agentStatus" label="当前状态" width="170">
                <template #default="scope">
                  <el-text :type="getStatusType(scope.row.agentStatus, 'hw')">
                    {{ scope.row.agentStatus }}
                  </el-text>
                </template>
              </el-table-column>

            </el-table>

            <!-- 分页控制 -->
            <el-pagination class="margin-top-sm" @size-change="handleHwSizeChange" @current-change="handleHwCurrentChange"
              :current-page="hwCurrentPage" :page-size="hwPageSize" layout="total, ->, pager, sizes, jumper, ->"
              :total="hwTotal"></el-pagination>
          </div>

        </div>
      </div>
    </div>
    <!-- 今日话务弹窗 -->
    <div v-if="isModalT" class="dialog">
      <div class="dialog-bg">
        <div class="d-t  flex space-between align-center">

          <img class="d-t-b" src="../../src/assets/img/hw-service.png" alt="">
          <div class="dialog-title common-title">今日-{{ dTitle }}</div>
          <div class="d-t-c pointer" @click="closeD" @mouseenter="handleMouseEnter" @mouseleave="handleTMouseLeave"
            @mousedown="handleMouseDown" @mouseup="handleMouseUp">
            <img :src="currentImage" class="">
            <!-- 预加载所有状态图片 -->
            <img v-if="false" :src="normalImage" alt="预加载">
            <img v-if="false" :src="hoverImage" alt="预加载">
            <img v-if="false" :src="clickImage" alt="预加载">
          </div>
        </div>
        <!-- 内容 -->
        <div class="dialog-content">

          <div v-if="dTitle !== '话务满意量' && dTitle !== '平均通话时长'" class="complaint-table-container">

            <el-table v-if="trafficIndex < 2" :data="tableData" stripe highlight-current-row height="486px">
              <el-table-column type="index" label="序号" width="60"></el-table-column>
              <el-table-column prop="caller" label="主叫号码" width="130"></el-table-column>
              <el-table-column prop="callee" label="被叫号码" width="130"></el-table-column>
              <el-table-column prop="agentName" label="接待客服" width="120"></el-table-column>
              <el-table-column prop="extension" label="分机号码" width="120"></el-table-column>
              <el-table-column v-if="trafficIndex != 3" prop="queueDuration" label="排队时长" width="150"></el-table-column>
              <el-table-column prop="ringDuration" label="振铃时长" width="150"></el-table-column>
              <el-table-column prop="talkDuration" label="通话时长" width="120"></el-table-column>
              <el-table-column prop="answertime" label="通话时间" width="120"></el-table-column>
              <el-table-column prop="hanguptime" label="挂断时间" width="150"></el-table-column>
              <el-table-column prop="hasrecord" label="是否录音" width="150">
                <template #default="scope">
                  <el-text :type="getStatusType(scope.row.hasrecord)">{{ scope.row.hasrecord }}</el-text>
                </template>
              </el-table-column>
            </el-table>
            <el-table v-if="trafficIndex == 2" :data="tableData" stripe highlight-current-row height="486px">
              <el-table-column type="index" label="序号" width="70"></el-table-column>
              <el-table-column prop="caller" label="主叫号码" width="300"></el-table-column>
              <el-table-column prop="callee" label="被叫号码" width="300"></el-table-column>
              <el-table-column prop="extension" label="分机号码" width="230"></el-table-column>
              <el-table-column prop="queueDuration" label="排队时长" width="230"></el-table-column>
              <el-table-column prop="ringDuration" label="振铃时长" width="220"></el-table-column>
            </el-table>
            <el-table v-if="trafficIndex == 3" :data="tableData" stripe highlight-current-row height="486px">
              <el-table-column type="index" label="序号" width="60"></el-table-column>
              <el-table-column prop="caller" label="主叫号码" width="130"></el-table-column>
              <el-table-column prop="callee" label="被叫号码" width="130"></el-table-column>
              <el-table-column prop="agentName" label="接待客服" width="120"></el-table-column>
              <el-table-column prop="extension" label="分机号码" width="120"></el-table-column>
              <el-table-column prop="ringDuration" label="振铃时长" width="150"></el-table-column>
              <el-table-column prop="talkDuration" label="通话时长" width="130"></el-table-column>
              <el-table-column prop="answertime" label="通话时间" width="120"></el-table-column>
              <el-table-column prop="hanguptime" label="挂断时间" width="150"></el-table-column>
              <el-table-column prop="hasrecord" label="是否录音" width="120">
                <template #default="scope">
                  <el-text :type="getStatusType(scope.row.hasrecord)">{{ scope.row.hasrecord }}</el-text>
                </template>
              </el-table-column>
            </el-table>


            <!-- 分页控制 -->
            <el-pagination class="margin-top-sm" @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-size="pageSize" layout="total, ->, pager, sizes, jumper, ->"
              :total="total"></el-pagination>
          </div>
          <!-- 话务满意量 -->
          <div v-else class="complaint-table-container">
            <el-table :data="allTableData" stripe highlight-current-row style="height: 486px;">
              <el-table-column type="index" label="序号" width="60"></el-table-column>
              <el-table-column prop="calltype" label="呼叫类型" width="120"></el-table-column>
              <el-table-column prop="caller" label="电话号码" width="140"></el-table-column>
              <el-table-column prop="agentName" label="接待客服" width="100"></el-table-column>
              <el-table-column prop="extension" label="分机号码" width="100"></el-table-column>
              <el-table-column prop="calltime" label="呼叫时间" width="140"></el-table-column>
              <el-table-column prop="hanguptime" label="挂断时间" width="140"></el-table-column>
              <el-table-column prop="evaluatename" label="满意度" width="100">
                <template #default="scope">
                  <div class="satisfaction-level">
                    <el-text :type="getStatusType(scope.row.evaluatename, 'satisfaction')">
                      {{ scope.row.evaluatename || '未评价' }}
                    </el-text>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="evaluatestars" label="满意度打分" width="150">
                <template #default="scope">
                  <div class="star-rating">
                    <img v-for="i in 5" :key="i" :src="i <= scope.row.evaluatestars ? scoringClicks : scorings" alt="评分星星"
                      class="star-icon">
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页控制 -->

            <el-pagination class="margin-top" @size-change="handlePageSizeChange"
              @current-change="handleCurrentPageChange" :current-page="pagination.currentPage"
              :page-size="pagination.pageSize" layout="total, ->, pager, sizes, jumper, ->"
              :total="pagination.totalRecords"></el-pagination>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, onUnmounted } from 'vue';
import { get, post } from '../utils/request'; // 假设这是您的HTTP请求工具
import { useTransition } from '@vueuse/core'
import normalImg from '../../src/assets/img/close-btn.png'
import hoverImg from '../../src/assets/img/close-hover.png'
import clickImg from '../../src/assets/img/close-click.png'
import callsIcon from '../../src/assets/img/calls-icon.png'
import answerNumIcon from '../../src/assets/img/answer-num-icon.png'
import lineIcon from '../../src/assets/img/line-icon.png'
import callIcon from '../../src/assets/img/call-icon.png'
import satisfiedIcon from '../../src/assets/img/satisfied-icon.png'
import averageIcon from '../../src/assets/img/average-icon.png'
import scoringClick from '../../src/assets/img/scoring-click.png'
import scoring from '../../src/assets/img/scoring-icon.png'
// 话务服务组件 - Vue3 JS格式
export default {
  name: 'TrafficService',

  // 组件配置
  setup() {
    // 状态管理
    let intervalId;
    let intervalId1;

    const isHovered = ref(false);
    const isClicked = ref(false);
    const isModal = ref(false);
    const isModalT = ref(false);
    const dTitle = ref('');
    const scoringClicks = ref(scoringClick);
    const scorings = ref(scoring);

    // 弹窗图片状态
    const normalImage = ref(normalImg);
    const hoverImage = ref(hoverImg);
    const clickImage = ref(clickImg);
    const currentImage = ref(normalImg);

    // 计算属性
    const hoverClass = computed(() => isHovered.value ? 'hoverBtn' : '');
    const clickClass = computed(() => isClicked.value ? 'clickBtn' : '');

    // 弹窗事件处理
    const handleMouseEnter = () => {
      currentImage.value = hoverImage.value;
    };

    const handleTMouseLeave = () => {
      currentImage.value = normalImage.value;
    };

    const handleMouseDown = () => {
      currentImage.value = clickImage.value;
    };

    const handleMouseUp = (event) => {
      const isMouseOver = event.relatedTarget?.closest('.d-t-c') !== null;
      currentImage.value = isMouseOver ? hoverImage.value : normalImage.value;
    };

    // const valueTransitions = new Map();
    const valueStates = reactive({})

    // const subValueTransitions = new Map();
    const subValueStates = reactive({});

    // 获取环比值的过渡对象
    const getSubValueOutput = (subValue, type) => {
      if (!type || subValue === undefined || subValue === null || isNaN(Number(subValue))) {
        return { value: 0 };
      }

      const targetValue = Math.floor(Number(subValue));
      const key = `subValueer_${type}`;

      // 1. 首次加载：初始化时 current 设为 0，target 设为目标值（触发从 0 开始的滚动）
      if (!subValueStates[key]) {
        subValueStates[key] = {
          current: 0, // 首次从 0 开始
          target: targetValue,
          timer: null
        };
        // 强制触发首次动画（从 0 → targetValue）
        const state = subValueStates[key];
        startAnimation1(state, key);
        return { value: 0 }; // 初始显示 0
      }

      const state = subValueStates[key];

      // 2. 后续更新：目标值变化时才触发动画
      if (state.target !== targetValue) {
        state.target = targetValue;
        startAnimation1(state, key); // 从当前值滚动到新目标值
      }

      // 返回当前整数（向下取整）
      return { value: Math.floor(state.current) };
    };
    const startAnimation1 = (state, key) => {
      // 清除之前的计时器（避免冲突）
      if (state.timer) {
        clearInterval(state.timer);
      }

      // 启动动画（每16ms更新一次）
      state.timer = setInterval(() => {
        const diff = state.target - state.current;

        // 差值足够小时，直接到位并停止
        if (Math.abs(diff) < 1) {
          state.current = state.target;
          clearInterval(state.timer);
          state.timer = null;
        } else {
          // 每次移动差值的 10%（平滑减速）
          state.current += diff * 0.1;
        }

        // 强制更新响应式（关键：确保Vue检测到变化）
        subValueStates[key] = { ...state };
      }, 16);
    };
    // 获取计数值的过渡对象
    const getValueOutput = (value, type) => {
      if (!type || value === undefined || value === null || isNaN(Number(value))) {
        return { value: 0 };
      }

      const targetValue = Math.floor(Number(value));
      const key = `valueer_${type}`;

      // 1. 首次加载：初始化时 current 设为 0，target 设为目标值（触发从 0 开始的滚动）
      if (!valueStates[key]) {
        valueStates[key] = {
          current: 0, // 首次从 0 开始
          target: targetValue,
          timer: null
        };
        // 强制触发首次动画（从 0 → targetValue）
        const state = valueStates[key];
        startAnimation2(state, key);
        return { value: 0 }; // 初始显示 0
      }

      const state = valueStates[key];

      // 2. 后续更新：目标值变化时才触发动画
      if (state.target !== targetValue) {
        state.target = targetValue;
        startAnimation2(state, key); // 从当前值滚动到新目标值
      }

      // 返回当前整数（向下取整）
      return { value: Math.floor(state.current) };
    };

    const startAnimation2 = (state, key) => {
      // 清除之前的计时器（避免冲突）
      if (state.timer) {
        clearInterval(state.timer);
      }

      // 启动动画（每16ms更新一次）
      state.timer = setInterval(() => {
        const diff = state.target - state.current;

        // 差值足够小时，直接到位并停止
        if (Math.abs(diff) < 1) {
          state.current = state.target;
          clearInterval(state.timer);
          state.timer = null;
        } else {
          // 每次移动差值的 10%（平滑减速）
          state.current += diff * 0.1;
        }

        // 强制更新响应式（关键：确保Vue检测到变化）
        valueStates[key] = { ...state };
      }, 16);
    };

    // 按钮事件处理
    const handleHover = () => {
      isHovered.value = true;
    };

    const handleMouseLeave = () => {
      isHovered.value = false;
    };

    const handleClick = () => {
      isClicked.value = !isClicked.value;
      showModal();
    };

    // 显示弹窗
    const showModal = (data) => {
      isModal.value = true;
      getAgentdetail()
    };

    // 显示今日话务弹窗
    const showTModal = (data, index) => {
      isModalT.value = true;
      dTitle.value = data;
      trafficIndex.value = index
      if (index == 4 || index == 5) {
        getEvaluationdetail()
      } else {
        getTdetail()
      }
    };

    // 关闭弹窗
    const closeD = () => {
      isModal.value = false;
      isModalT.value = false;
      tableData.value = []
      allTableData.value = []
      currentImage.value = normalImage.value

    };

    // 过渡中间变量（每次从0开始）
    const agentOnCallTransition = ref(0);
    const agentBusyTransition = ref(0);
    const agentIdleTransition = ref(0);
    const agentOfflineTransition = ref(0);
    const agentCountTransition = ref(0);

    const agentOnCallOutput = useTransition(agentOnCallTransition, {
      duration: 1500, // 动画时长
    });
    const agentBusyOutput = useTransition(agentBusyTransition, {
      duration: 1500, // 动画时长
    });
    const agentIdleOutput = useTransition(agentIdleTransition, {
      duration: 1500, // 动画时长
    });
    const agentOfflineOutput = useTransition(agentOfflineTransition, {
      duration: 1500, // 动画时长
    });
    const agentCountOutput = useTransition(agentCountTransition, {
      duration: 1500, // 动画时长
    });

    // 初始创建过渡实例
    // let transitions = createTransitions();

    // 辅助函数：生成晚于给定时间的随机时间
    const generateTimeAfter = (time) => {
      const [hours, minutes, seconds] = time.split(':').map(Number);
      let newHours = hours;
      let newMinutes = minutes + Math.floor(Math.random() * 10);
      let newSeconds = seconds + Math.floor(Math.random() * 30);

      if (newSeconds >= 60) {
        newMinutes += 1;
        newSeconds -= 60;
      }

      if (newMinutes >= 60) {
        newHours += 1;
        newMinutes -= 60;
      }

      return `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}:${newSeconds.toString().padStart(2, '0')}`;
    };



    // 数据和分页状态
    const tableData = ref([]);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref();


    // 状态类型映射 - 增加满意度相关的状态类型
    const getStatusType = (status, type = 'recording') => {
      if (type === 'recording') {
        return status === '是' ? 'success' : 'warning';
      } else if (type === 'hw') {
        const statusMap = {
          '在线': 'success',
          '示忙': 'warning',
          '示闲': 'primary',
          '离线': 'info',
          '通话中': 'success'
        };
        return statusMap[status] || 'default';
      }
      else if (type === 'satisfaction') {
        // 根据满意度级别返回不同的状态类型
        const satisfactionMap = {
          '非常满意': 'success',
          '满意': 'primary',
          '一般': 'info',
          '不满意': 'warning',
          '非常不满意': 'danger'
        };
        return satisfactionMap[status] || 'default';
      }
      return 'default';
    };

    // 分页事件处理
    const handleSizeChange = (newSize) => {
      pageSize.value = newSize;
      currentPage.value = 1; // 重置到第一页
      getTdetail()

    };

    const handleCurrentChange = (newPage) => {
      currentPage.value = newPage;
      getTdetail()

    };

    // 话务数据配置
    const trafficData = ref([
      {
        type: 'callVolume',  // 来电数/话务量类型
        title: '来电数/话务量',
        unit: '个',
        icon: callsIcon,
        subtitle: '环比',
        subUnit: '%',
        subValue: '',
        value: ''
      },
      {
        type: 'answerVolume',  // 接听量类型
        title: '接听量',
        unit: '个',
        icon: answerNumIcon,
        subtitle: '接通率',
        subUnit: '%',
      },
      {
        type: 'queueVolume',  // 队列中类型
        title: '队列中',
        unit: '个',
        icon: lineIcon,
        subtitle: '放弃率',
        subUnit: '%',
      },
      {
        type: 'outboundVolume',  // 外呼量类型
        title: '外呼量',
        unit: '个',
        icon: callIcon,
        subtitle: null
      },
      {
        type: 'satisfiedVolume',  // 话务满意量类型
        title: '话务满意量',
        unit: '个',
        icon: satisfiedIcon,
        subtitle: '满意度',
        subUnit: '%',
      },
      {
        type: 'averageDuration',  // 平均通话时长类型
        title: '平均通话时长',
        unit: 'MIN',
        icon: averageIcon,
        subtitle: null
      }
    ]);
    const trafficIndex = ref('')
    // 话务满意量数据处理
    // 原始数据（全部数据）
    const allTableData = ref([]);

    // 使用reactive对象封装分页相关数据
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      totalRecords: 20
    });
    // 分页事件处理
    const handlePageSizeChange = (newSize) => {
      pagination.pageSize = newSize;
      pagination.currentPage = 1; // 重置页码
      getEvaluationdetail()

    };

    const handleCurrentPageChange = (newPage) => {
      pagination.currentPage = newPage;
      getEvaluationdetail()

    };

    // 表格自适应处理
    const tableContainerRef = ref(null);
    const containerWidth = ref(0);

    const handleWindowResize = () => {
      updateContainerWidth();
    };

    const updateContainerWidth = () => {
      if (tableContainerRef.value) {
        containerWidth.value = tableContainerRef.value.clientWidth;
      }
    };

    const handleTableResize = () => {
      updateContainerWidth();
    };

    // 实时坐席数据
    const hwCurrentPage = ref(1);
    const hwPageSize = ref(10);
    const hwTotal = ref(0);

    const hwData = ref([]);

    // 分页事件处理（添加hw前缀）
    const handleHwSizeChange = (newSize) => {
      hwPageSize.value = newSize;
      hwCurrentPage.value = 1; // 重置到第一页
      getAgentdetail()
    };

    const handleHwCurrentChange = (newPage) => {
      hwCurrentPage.value = newPage;
      getAgentdetail()
    };


    // 获取今日话务数据（每5秒执行）
    const getTodayData = async () => {
      try {
        const response = await get('/cloudcall-flowable-api/shenyang-report/screen/today-call');
        trafficData.value[0].value = response.data.callInCount;
        trafficData.value[0].subValue = response.data.callInCountDayToDay;
        trafficData.value[1].value = response.data.callInAnswerCount;
        trafficData.value[1].subValue = response.data.callInAnswerRate;
        trafficData.value[2].value = response.data.inqueueCount;
        trafficData.value[2].subValue = response.data.giveUpRate;
        trafficData.value[3].value = response.data.callOutCount;
        trafficData.value[4].value = response.data.callInSatisfactionCount;
        trafficData.value[4].subValue = response.data.callInSatisfactionRate;
        trafficData.value[5].value = response.data.callInTalkdurationAvg;
      } catch (error) {
        console.error('获取今日话务数据失败', error);
      }
    };
    // 获取坐席数据（每5秒执行一次）
    const getAgentData = async () => {
      try {
        const response = await get('/cloudcall-flowable-api/shenyang-report/screen/realtime-agent');

        agentOnCallTransition.value = Number(response.data.agentOnCallCount) || 0;
        agentBusyTransition.value = Number(response.data.agentBusyCount) || 0;
        agentIdleTransition.value = Number(response.data.agentIdleCount) || 0;
        agentOfflineTransition.value = Number(response.data.agentOfflineCount) || 0;
        agentCountTransition.value = Number(response.data.agentCount) || 0;

      } catch (error) {
        console.error('获取坐席数据失败', error);
      }
    };
    // 获取坐席详情数据
    const getAgentdetail = async () => {
      try {
        let data = {
          pageNum: hwCurrentPage.value,
          pageSize: hwPageSize.value
        }
        const response = await get('/cloudcall-flowable-api/shenyang-report/screen/realtime-agent/detail', data);
        hwData.value = response.data.content;
        hwTotal.value = response.data.totalElements;
      } catch (error) {
        console.error('获取坐席详情数据失败', error);
      }
    };
    // 获取话务详情数据
    const getTdetail = async () => {
      try {
        let data = {
          pageNum: currentPage.value,
          pageSize: pageSize.value
        }
        let response = ''
        if (trafficIndex.value == 0) {
          response = await get('/cloudcall-flowable-api/shenyang-report/screen/today-callin', data);

        }
        if (trafficIndex.value == 1) {
          response = await get('/cloudcall-flowable-api/shenyang-report/screen/today-callin-answered', data);
        }
        if (trafficIndex.value == 2) {
          response = await get('/cloudcall-flowable-api/shenyang-report/screen/today-call-inqueue', data);

        }
        if (trafficIndex.value == 3) {
          response = await get('/cloudcall-flowable-api/shenyang-report/screen/today-callout', data);

        }

        tableData.value = response.data.content;
        total.value = response.data.totalElements;
      } catch (error) {
        console.error('获取话务详情数据失败', error);
      }
    };
    // 获取满意量数据
    const getEvaluationdetail = async () => {
      try {
        let data = {
          pageNum: pagination.currentPage,
          pageSize: pagination.pageSize
        }
        let response = ''
        if (trafficIndex.value == 4) {
          response = await get('/cloudcall-flowable-api/shenyang-report/screen/today-call-evaluation', data);

        }
        if (trafficIndex.value == 5) {
          response = await get('/cloudcall-flowable-api/shenyang-report/screen/today-call-avgTalkDuration', data);

        }


        allTableData.value = response.data.content;
        pagination.totalRecords = response.data.totalElements;
      } catch (error) {
        console.error('获取满意量数据失败', error);
      }
    };

    // 生命周期钩子 - 组件挂载后执行
    onMounted(() => {

      // 首次加载数据
      getAgentData();
      getTodayData();

      // 启动5秒定时器，每5秒刷新一次数据并触发轮播
      intervalId1 = setInterval(getAgentData, 5000);

      intervalId = setInterval(getTodayData, 5000);

      // 使用nextTick确保DOM已完全渲染
      nextTick(() => {
        updateContainerWidth();
        window.addEventListener('resize', handleWindowResize);
      });
    });
    onUnmounted(() => {
      clearInterval(intervalId);
      clearInterval(intervalId1);
      intervalId = null;
      intervalId1 = null;

    })


    // 生命周期钩子 - 组件卸载前清理
    onBeforeUnmount(() => {
      window.removeEventListener('resize', handleWindowResize);
    });
    // 返回所有需要在模板中使用的变量和方法
    return {
      // ...transitions,
      isHovered,
      getTodayData,
      getAgentData,
      showModal,
      trafficIndex,
      showTModal,
      isModal,
      isModalT,
      isClicked,
      getAgentdetail,
      getEvaluationdetail,
      getTdetail,
      hoverClass,
      clickClass,
      handleHover,
      handleMouseLeave,
      handleClick,
      getValueOutput,
      getSubValueOutput,
      trafficData,
      dTitle,
      currentImage,
      normalImage,
      hoverImage,
      clickImage,
      tableData,
      currentPage,
      pageSize,
      total,
      getStatusType,
      hwData,
      handleSizeChange,
      handleCurrentChange,
      handleTMouseLeave,
      handleMouseEnter,
      handleMouseDown,
      handleMouseUp,
      closeD,
      // 话务满意量相关
      pagination,
      allTableData,
      handlePageSizeChange,
      handleCurrentPageChange,
      // 表格自适应相关
      tableContainerRef,
      containerWidth,
      handleTableResize,
      // 坐席人数相关
      hwCurrentPage,
      hwPageSize,
      hwTotal,
      scorings,
      scoringClicks,
      agentOnCallOutput,
      agentBusyOutput,
      agentIdleOutput,
      agentOfflineOutput,
      agentCountOutput,
      agentOnCallTransition,
      agentBusyTransition,
      agentIdleTransition,
      agentOfflineTransition,
      agentCountTransition,
      handleHwSizeChange,
      handleHwCurrentChange
    };
  }
};
</script>

<style scoped>
/* 话务 */
.traffic {}

.traffic-bg {
  background-image: url('../../src/assets/img/huawu.png');
  background-size: 100% 41px;
  margin-top: 77px;
  height: 41px;
}

.traffic-title {
  height: 21px;
  padding-bottom: 10px;
}

.traffic-bg img {
  width: 152px;
  height: 52px;
}

/* 实时坐席 */
.traffic-subtitle {}

.seats-bg {
  padding: 0 54px;
  /* seats-title-bg */
  background-image: url('../../src/assets/img/seats-title-bg.png');
  background-size: 100% 168px;
  height: 168px;
  background-repeat: no-repeat;
}

.seats-bg>div {
  height: 168px;
}

.seats-bg img {
  height: 79px;
  width: 72px;
}

:deep(.el-statistic.seats-title .el-statistic__content) {
  font-family: DIN-Bold;
  font-weight: bold;
  font-size: 32px;
  font-weight: 400;
  color: transparent;
  background: linear-gradient(0deg, #86cbf0 0%, #ffffff 90%);
  -webkit-background-clip: text;
  filter: drop-shadow(0 1px 2px rgba(14, 22, 51, 0.15)) drop-shadow(0 2px 4px rgba(14, 22, 51, 0.2)) drop-shadow(0 4px 8px rgba(14, 22, 51, 0.25)) drop-shadow(0 8px 16px rgba(14, 22, 51, 0.3)) drop-shadow(0 16px 32px rgba(14, 22, 51, 0.35));

}

.ren {
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 22px;
}

/* 坐席人数 按钮 */
.check-more {
  background-image: url('../../src/assets/img/check-btn.png');
}

.hoverBtn {
  background-image: url('../../src/assets/img/hover-btn.png');

}

.clickBtn {
  background-image: url('../../src/assets/img/click-btn.png');

}

.el-table {
  /* height: 450px !important; */
}

/* 话务 */
.common-subtitle .num-calls {
  background-image: url('../../src/assets/img/answer.png');
  background-size: 98%;
  /* width: 741px; */
}

.common-subtitle>.margin-tb-sm {
  margin-bottom: 5px;
}

.d-t .dialog-bg {
  background-image: url('/src/assets/img/d-t-b.png');
  max-height: 665px;
  /* width: 1012px; */
  height: 665px;
  margin: 10px 0;

}

.num-calls.active,
.num-calls:hover {
  background-image: url('../../src/assets/img/calls.png');
  height: 56px;
  position: relative;
  background-size: 100% 56px;
  cursor: pointer;
}

.num-calls>div {
  height: 56px;
}

.num-img {
  width: 70px;
  height: 56px;
}

.num-calls .line {
  height: 40px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.num-subtitle {
  width: 100%;
}

.num-right {
  line-height: 56px;
}


.complaint-table-container {
  margin: 20px 0;
}

.star-icon {
  width: 16px;
  height: 16px;
}

.satisfaction-level {
  display: inline-flex;
  align-items: center;
}

.satisfaction-level .el-text {
  margin-right: 8px;
}

.num-calls :deep(.el-statistic .el-statistic__content) {
  font-size: 28px;

}

:deep(.el-statistic.color-red .el-statistic__content) {
  color: #FF6A6A !important;


}

:deep(.el-statistic.color-green .el-statistic__content) {
  color: #4EFFAE !important;


}
</style>