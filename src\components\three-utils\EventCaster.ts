import * as THREE from 'three';
export class EventCaster {
  private clickListeners: { obj: THREE.Object3D, callback: (result: THREE.Intersection) => void }[] = []
  private hoverListeners: { obj: THREE.Object3D, callback: (result: THREE.Intersection) => void }[] = []
  private rayCaster: THREE.Raycaster = new THREE.Raycaster()
  private mouse: THREE.Vector2 = new THREE.Vector2()
  public camera: THREE.Camera        
  public canvas: HTMLCanvasElement   
  public defaultCilckCallback: () => void = () => { }   
  public defaultHoverCallback: () => void = () => { }   
  constructor(camera: THREE.Camera, canvas: HTMLCanvasElement) {
    this.camera = camera
    this.canvas = canvas
    this.canvas.addEventListener('mousemove', (event) => {
      this.mouse.x = (event.offsetX / this.canvas.offsetWidth) * 2 - 1
      this.mouse.y = -(event.offsetY / this.canvas.offsetHeight) * 2 + 1
      this.rayCaster.setFromCamera(this.mouse, this.camera)
      const intersects = this.rayCaster.intersectObjects(this.hoverListeners.map(l => l.obj))
      if (intersects.length > 0) {
        const result = intersects[0]
        const listener = this.hoverListeners.find((l: any) => l.obj.id === result.object.id)
        listener?.callback(result)
      } else {
        this.defaultHoverCallback()
      }
    })
    this.canvas.addEventListener('click', () => {
      this.rayCaster.setFromCamera(this.mouse, this.camera)
      const intersects = this.rayCaster.intersectObjects(this.clickListeners.map(l => l.obj))
      if (intersects.length > 0) {
        const result = intersects[0]
        const listener = this.clickListeners.find((l: any) => l.obj.id === result.object.id)
        listener?.callback(result)
      } else {
        this.defaultCilckCallback()
      }
    })
  }
  addListener(obj: THREE.Object3D | THREE.Object3D[], event: keyof HTMLElementEventMap, callback: (result: THREE.Intersection) => void) {
    if (Array.isArray(obj)) {
      obj.forEach(o => {
        if (event === 'click') {
          this.clickListeners.push({ obj: o, callback: callback })
        }
        else if (event === 'mousemove') {
          this.hoverListeners.push({ obj: o, callback: callback })
        }
      })
    } else {
      if (event === 'click') {
        this.clickListeners.push({ obj, callback: callback })
      }
      else if (event === 'mousemove') {
        this.hoverListeners.push({ obj, callback: callback })
      }
    }
  }
  dispose() {
    this.canvas.removeEventListener('mousemove', (event) => {
      this.mouse.x = (event.offsetX / this.canvas.offsetWidth) * 2 - 1
      this.mouse.y = -(event.offsetY / this.canvas.offsetHeight) * 2 + 1
      this.rayCaster.setFromCamera(this.mouse, this.camera)
      const intersects = this.rayCaster.intersectObjects(this.hoverListeners.map(l => l.obj))
      if (intersects.length > 0) {
        const result = intersects[0]
        const listener = this.hoverListeners.find((l: any) => l.obj.id === result.object.id)
        listener?.callback(result)
      } else {
        this.defaultHoverCallback()
      }
    })
    this.canvas.removeEventListener('click', () => {
      this.rayCaster.setFromCamera(this.mouse, this.camera)
      const intersects = this.rayCaster.intersectObjects(this.clickListeners.map(l => l.obj))
      if (intersects.length > 0) {
        const result = intersects[0]
        const listener = this.clickListeners.find((l: any) => l.obj.id === result.object.id)
        listener?.callback(result)
      } else {
        this.defaultCilckCallback()
      }
    })
  }
}
