import { post } from './request';
import { ElMessage } from 'element-plus';

export const handleLogin = async (jobNo) => {
    try {
        if (!jobNo) throw new Error('未获取到工号');

        const response = await post('/auth-api/oauth2/tokenByJobNo', {
            jobNo,
            systemCode: 'HOTLINE'
        });

        if (response && response.token) {
            sessionStorage.setItem('token', response.token);
            sessionStorage.setItem('userid', response.id);
            sessionStorage.setItem('tenantId', response.tenantId);
            sessionStorage.setItem('jobNo', response.jobNo);
            return true;
        }

        throw new Error('认证失败，未获取到token');
    } catch (error) {
        console.error('登录失败:', error);
        ElMessage.error(`登录失败: ${error.message}`);
        return false;
    }
};