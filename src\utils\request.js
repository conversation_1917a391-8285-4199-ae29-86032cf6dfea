import axios from 'axios';
import { ElMessage } from 'element-plus';
import { handleLogin } from './auth';

// 创建 axios 实例
const service = axios.create({
  baseURL: '/api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
});

// 防止重复请求的标识
let isRefreshing = false;
let requests = [];

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    const token = sessionStorage.getItem('token');
    const tenantId = sessionStorage.getItem('tenantId');
    const userid = sessionStorage.getItem('userid');

    // 登录接口特殊处理
    if (config.url.includes('/auth-api/oauth2/tokenByJobNo')) {
      config.headers['Content-Type'] = 'application/json';
      delete config.headers['x-userid-header'];
      delete config.headers['x-tenantid-header'];
      return config;
    }

    // 其他接口添加认证信息
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    if (userid) config.headers['x-userid-header'] = userid;
    if (tenantId) config.headers['x-tenantid-header'] = tenantId;

    return config;
  },
  (error) => {
    console.error('请求错误：', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 登录接口直接返回完整响应
    if (response.config.url.includes('/auth-api/oauth2/tokenByJobNo')) {
      return response.data;
    }

    // 业务逻辑处理
    const { data } = response;
    if (data.code === '100000') {
      return data;
    }

    console.error('业务错误：', data.message);
    ElMessage.error(data.message || '请求失败');
    return Promise.reject(new Error(data.message || '请求失败'));
  },
  async (error) => {
    const { config, response } = error;

    // 处理 HTTP 状态码错误
    if (response && response.status === 401) {
      // 避免重复刷新token
      if (!isRefreshing) {
        isRefreshing = true;

        try {
          // 获取当前请求的jobNo
          const jobNo = sessionStorage.getItem('jobNo') || new URLSearchParams(window.location.search).get('jobNo');
          if (!jobNo) throw new Error('未获取到工号');

          // 刷新token
          const loginSuccess = await handleLogin(jobNo);

          if (loginSuccess) {
            // 执行队列中的请求
            requests.forEach(cb => cb());
            requests = [];

            // 重试原请求
            const newToken = sessionStorage.getItem('token');
            config.headers.Authorization = `Bearer ${newToken}`;
            return service(config);
          } else {
            ElMessage.error('重新登录失败，请手动刷新页面');
            return Promise.reject(error);
          }
        } catch (refreshError) {
          ElMessage.error('刷新登录状态失败，请重新登录');
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      } else {
        // 等待token刷新时，将请求缓存
        return new Promise(resolve => {
          requests.push(() => {
            const newToken = sessionStorage.getItem('token');
            config.headers.Authorization = `Bearer ${newToken}`;
            resolve(service(config));
          });
        });
      }
    }

    // 处理其他HTTP错误
    let message = '请求失败';
    if (response) {
      switch (response.status) {
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求错误，未找到该资源';
          break;
        case 500:
          message = '服务器错误';
          break;
        default:
          message = `连接错误 ${response.status}`;
      }
      ElMessage.error(message);
    }

    console.error('响应错误：', message);
    return Promise.reject(error);
  }
);

export const get = (url, params) => service.get(url, { params });
export const post = (url, data) => service.post(url, data);
export const put = (url, data) => service.put(url, data);
export const del = (url) => service.delete(url);

export default service;