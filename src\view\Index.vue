<template>
  <v-scale-screen ref="scale-screen" width="3120" height="1248" :fullScreen="true">
    <div id="index" class="">
      <!-- 加载状态显示 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载数据...</div>
      </div>



      <!-- 主内容区域，仅在认证成功后显示 -->
      <div v-else>
        <div class="bg">
          <img src="../../src/assets/img/bg.png" alt="">

        </div>
        <div class="common-layout padding-lr-lg">
          <el-container>
            <!-- <el-header>Header</el-header> -->
            <!-- <el-container> -->
            <el-aside class="padding-tb-sm" width="800px">
              <div>
                <!-- 时间 -->
                <div class="flex align-center ">
                  <div class="times Roboto">{{ formattedTime }}
                  </div>
                  <div class="line margin-left-sm"></div>

                  <div class="font-size-sm color-blue   margin-lr-sm">
                    <div>{{ formattedDate }}</div>
                    <div>{{ weekDay }}</div>
                  </div>

                </div>
                <!-- 话务服务 -->
                <TrafficService />
                <!-- 停水公告 -->
                <Announcement />

              </div>

            </el-aside>
            <!-- 中间 -->
            <el-main class="main-title">
              <Main />

            </el-main>
            <el-aside class="padding-top" style="overflow: hidden;" width="800px">
              <!-- 全屏 -->
              <div @click="toggleFullScreen" class="flex-end font-size-sl color-blue flex align-center"><img
                  class="margin-right-xs" src="../assets/img/full.png" alt="">
                <span v-if="!isFullScreen">全屏</span>
                <span v-else>取消全屏</span>
              </div>
              <!-- 工单服务 -->
              <OrderService />
              <!-- 问题板块 -->
              <Question />
              <!-- 服务评分排名 -->
              <!-- <Score /> -->
               <!-- 超时件排名 -->
               <TimeoutRanking />
            </el-aside>
          </el-container>
          <!-- </el-container> -->
        </div>
      </div>
    </div>
  </v-scale-screen>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { get, post } from '../utils/request';
import { ElMessage } from 'element-plus';
import VScaleScreen from 'v-scale-screen'
import TrafficService from '../components/TrafficService.vue';
import Announcement from '../components/Announcement.vue';
import Main from '../components/MainContent.vue';
import OrderService from '../components/OrderService.vue';
import Question from '../components/Question.vue';
import Score from '../components/Score.vue';
import TimeoutRanking from '../components/TimeoutRanking.vue';
import { handleLogin } from '../utils/auth';
export default {
  name: 'Index',
  components: {
    VScaleScreen,
    TrafficService,
    Announcement,
    Main,
    OrderService,
    Question,
    Score,
    TimeoutRanking
  },
  setup() {
    const sessionJobNo = ref(sessionStorage.getItem('jobNo'))

    // 认证状态管理
    const isLoading = ref(true);        // 加载状态
    const isAuthenticated = ref(false); // 认证状态
    const authFailed = ref(false);      // 认证失败状态

    const getJobNoFromUrl = () => {
      const params = new URLSearchParams(window.location.search);
      return params.get('jobNo');
    };

    const jobNo = ref(getJobNoFromUrl());
    // 日期时间计算
    const currentDate = ref(new Date());
    const formattedDate = computed(() => {
      const year = currentDate.value.getFullYear();
      const month = String(currentDate.value.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.value.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    });

    const formattedTime = computed(() => {
      const hours = String(currentDate.value.getHours()).padStart(2, '0');
      const minutes = String(currentDate.value.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    });

    const weekDay = computed(() => {
      const days = ['日', '一', '二', '三', '四', '五', '六'];
      return `星期${days[currentDate.value.getDay()]}`;
    });

    // 定时器引用
    let timer = null;

    const updateTime = () => {
      currentDate.value = new Date();
    };
    // 全屏状态标识
    const isFullScreen = ref(false);

    // 检查当前是否处于全屏状态
    const checkFullScreen = () => {
      return (
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
    };

    // 切换全屏状态
    const toggleFullScreen = () => {
      // 当前未全屏
      if (!checkFullScreen()) {
        // 尝试进入全屏
        const element = document.documentElement; // 全屏整个页面
        // const element = document.getElementById('target'); // 全屏指定元素

        if (element.requestFullscreen) {
          element.requestFullscreen().catch(err => {
            console.error(`进入全屏失败: ${err.message}`);
          });
        } else if (element.webkitRequestFullscreen) { // 兼容 Safari
          element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) { // 兼容 IE/Edge
          element.msRequestFullscreen();
        } else if (element.mozRequestFullScreen) { // 兼容 Firefox
          element.mozRequestFullScreen();
        }
      }
      // 当前已全屏
      else {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        }
      }
    };

    // 监听全屏状态变化
    const handleFullScreenChange = () => {
      isFullScreen.value = !!checkFullScreen();
    };


    onMounted(async () => {
      const existingToken = sessionStorage.getItem('token');
      if (existingToken && sessionJobNo.value === jobNo.value) {
        console.log('已存在有效token，直接进入系统');
        isAuthenticated.value = true;
        isLoading.value = false;
        updateTime();
        timer = setInterval(updateTime, 60000);
        return;
      }

      // 调用抽离的登录函数
      const loginSuccess = await handleLogin(jobNo.value);
      if (loginSuccess) {
        isAuthenticated.value = true;
      } else {
        authFailed.value = true;
      }
      isLoading.value = false;
      // 确保在DOM更新后再启动定时器
      await nextTick();
      updateTime();
      timer = setInterval(updateTime, 1000);
      document.addEventListener('fullscreenchange', handleFullScreenChange);
      document.addEventListener('webkitfullscreenchange', handleFullScreenChange);
      document.addEventListener('mozfullscreenchange', handleFullScreenChange);
      document.addEventListener('MSFullscreenChange', handleFullScreenChange);

      // 初始化状态检查
      isFullScreen.value = checkFullScreen();
    });
    // 格式化完整日期时间（包含秒）
    const formattedDateTime = computed(() => {
      const hours = String(currentDate.value.getHours()).padStart(2, '0');
      const minutes = String(currentDate.value.getMinutes()).padStart(2, '0');
      const seconds = String(currentDate.value.getSeconds()).padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
    });

    onUnmounted(() => {
      clearInterval(timer);
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullScreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullScreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullScreenChange);
    });



    return {
      formattedDate,
      formattedTime,
      weekDay,
      jobNo,
      isLoading,
      isAuthenticated,
      sessionJobNo,
      authFailed,
      isFullScreen,
      checkFullScreen,
      toggleFullScreen,
      handleFullScreenChange,
      formattedDateTime
    };
  }
};
</script>

<style scoped>
#index {
  font-size: 24px;
  color: white;
  text-align: center;

}

.bg {
  /* width: 100vh;
  height: 100%; */
  position: relative;
}

.common-layout {
  position: absolute;
  top: 0;
}

.times {
  font-size: 42px;
}

.main-title {
  margin: 0 !important;
  padding: 0 !important;
  position: relative;
}


/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  /* background-color: rgba(0, 0, 0, 0.7); */
  z-index: 1000;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 6px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #409EFF;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28px;
  color: white;
}

/* 认证失败样式 */
.auth-failed-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
}

.auth-failed-message {
  background-color: white;
  color: #f56c6c;
  padding: 20px 40px;
  border-radius: 8px;
  font-size: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.auth-failed-message i {
  margin-right: 10px;
  font-size: 28px;
}
</style>
